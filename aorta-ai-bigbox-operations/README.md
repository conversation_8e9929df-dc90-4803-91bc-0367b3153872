## 基于[oula](http://oula.sit.saas.htsc/)的 bigbox 微应用模版

本项目由 ht-cli 工具自动生成，本项目已默认接入 Doorkeeper 门禁。

**注意**：本项目默认适配 bigbox v2 版本，如果您的主应用是 bigbox v1 的，请务必将 @ht/umi-plugin-bigbox 的依赖版本降级为 0.x 版本。

## 环境准备

安装 `node_modules`:

```bash
yarn
```

## 基础脚本

### 本地开发环境启动项目

```bash
yarn start
```

### 构建项目

```bash
yarn build
```

### Lint

```bash
npm run lint
```

如果需要自动批量修复 lint 错误，可以使用：


```bash
npm run lint:fix
```
