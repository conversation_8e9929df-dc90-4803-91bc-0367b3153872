// https://umijs.org/config/
import { defineConfig } from '@oula/oula';

export default defineConfig({
  plugins: [
    // https://github.com/zthxxx/react-dev-inspector
    'react-dev-inspector/plugins/umi/react-inspector',
  ],
  // https://github.com/zthxxx/react-dev-inspector#inspector-loader-props
  inspectorConfig: {
    exclude: [],
    babelPlugins: [],
    babelOptions: {},
  },
  // mfsu: {},
  webpack5: {
    // 本地开发时默认关掉 lazyCompilation，避免在微前端集成时触发延迟构建请求地址错误. 如果仅以单应用形式提供服务则可以打开
    lazyCompilation: {
      // disable lazy compilation for dynamic imports
      imports: false,
      // disable lazy compilation for entries
      entries: false,
    },
  },
});
