// https://umijs.org/config/
import path from "path";
import { defineConfig } from "@oula/oula";
import proxy from "./proxy";
import routes from "./routes";
import theme from "./theme";
import BaseSettings from "./BaseSettings";

const { REACT_APP_ENV } = process.env;

function resolve(dir: string) {
  console.log(path.join(__dirname, "..", dir));
  return path.join(__dirname, "..", dir);
}
export default defineConfig({
  bundlerConfig: {
    output: {
      filenameHash: true,
      //生产环境下publicPath
      assetPrefix: `/${BaseSettings.appName}/`
    },
    dev: {
      //测试环境下publicPath
      assetPrefix: `/${BaseSettings.appName}/`
    },
    tools: {
      less: {
        lessOptions: {
          modifyVars: theme,
        },
      },
    },
    source: {
      alias: {
        '@config': resolve('config'),
      },
    },
    html: {
      title: '华泰证券',
    },
    performance: {
      removeMomentLocale: true,
    },
    server: {
      proxy: proxy[REACT_APP_ENV || 'dev'],
    },
  },
  dva: {
    hmr: true,
  },
  history: {
    type: 'hash',
  },
  locale: {
    default: 'zh-CN',
    antd: false,
    spriteui:true
  },
  antd:false,
  spriteui: {
    // theme: 'dark', // uncomment this line when using dark theme
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  // 为 hash 路由添加统一前缀，最终格式为 `${hashPrefix}${route.path}`
  plugins: ['@ht/umi-plugin-hashprefix'],
  hashPrefix: `/${BaseSettings.appName}`,
  routes,
  bigbox: {
    appName: BaseSettings.appName,
    outputJsonInDev: true,
    /**
     * 本地开发环境用于模拟集成环境的 getHost().getState() 返回数据
     */
    localState: {
      global: {
        userInfo: {
          name: "用户姓名（测试数据）",
          userId: "010212",
        },
      },
    },
  },
});
