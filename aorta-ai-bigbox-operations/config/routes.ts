﻿/**
 * !!! 注意: 这里的hash路由，在 umi 编译时会被统一添加上 ./config.ts 中的 hashPrefix 配置项
 */
// 是否需要自动生成菜单
const NEED_MENU = false;

export default [
  {
    path: '/',
    component: NEED_MENU ?'../layouts/MenuLayout': '../layouts/BasicLayout',
    routes: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        icon: '',
        component: './dashboard/workplace',
      },
      {
        path: '/term-operation/term-operation-list',
        icon: '',
        name: '术语列表',
        component: './term-operation/term-operation-list',
      },
      {
        name: '动态主题',
        icon: '',
        path: '/dyntheme',
        component: './dyn-theme',
      },
      {
        name: '魔方pc搭建页面',
        icon: '',
        path: '/lowcode-render',
        component: './lowcode-render',
        // target 为魔方搭建工作台地址
        target: 'http://lowcode.fe.htsc/app/b675695fe0f69d915ad1/editor?pageId=3946142acd7b2f33d353'
      },
    ],
  },
];
