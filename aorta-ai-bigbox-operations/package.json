{"name": "aorta-ai-bigbox-operations", "version": "0.1.0", "private": true, "description": "A project named aorta-ai-bigbox-operations", "keywords": "", "author": "<EMAIL>", "repository": "", "scripts": {"analyze": "cross-env BUNDLE_ANALYZE=true oula build", "build": "oula build", "dev": "cross-env oula dev", "postinstall": "oula g tmp", "lint": "oula g tmp && npm run lint:js && npm run lint:style", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx ./src", "lint:style": "stylelint --fix \"src/**/*.less\"", "start": "cross-env oula dev", "start:debug": "cross-env DEBUG=builder oula dev", "tsc": "tsc --noEmit", "eslint:output": "eslint --fix -f html > ./eslint-error.html", "prepare": "husky install && node ./.husky/prepare.js &&  husky install && node ./.husky/prepare.js"}, "dependencies": {"@ht-icons/sprite-ui-react": "^0.3.4", "@ht/advanced-table": "^0.5.5", "@ht/sprite-ui": "^1.3.0-beta.10", "@lowcode/lc-render": "^1.1.15", "ahooks": "^3.7.2", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "dva": "^2.4.1", "eslint-formatter-pretty": "4.1.0", "lodash-es": "^4.17.21", "lru-cache": "^6.0.0", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^17.0.2", "react-color": "^2.19.3", "react-dom": "^17.0.2", "umi-request": "^1.4.0"}, "devDependencies": {"@ht/eslint-config-htsc": "^2.0.14", "@ht/umi-plugin-bigbox": "^1.0.3", "@ht/umi-plugin-hashprefix": "^0.1.4", "@ht/webpack-plugin-concat-jobid": "^2.0.3", "@oula/oula": "^1.1.3", "@oula/plugin-bigbox": "^1.1.2", "@oula/preset-react-pc": "^3.0.4", "@types/classnames": "^2.3.1", "@types/express": "^4.17.14", "@types/lodash-es": "^4.17.6", "@types/lru-cache": "^5.1.1", "@types/react": "^18.0.25", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.0.9", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^5", "cross-env": "^7.0.3", "eslint": "^7.32.0", "husky": "7.0.4", "postcss": "^8.4.16", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "react-dev-inspector": "^1.8.1", "stylelint": "^14.9.1", "typescript": "^5"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "engines": {"node": ">=10.0.0"}, "templateConfig": {"name": "template-pc-bigbox-app", "version": "1.0.0"}}