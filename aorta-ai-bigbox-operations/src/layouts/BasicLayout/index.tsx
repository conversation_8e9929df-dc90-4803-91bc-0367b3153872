import BaseSettings from '@config/BaseSettings';
import { Button, ConfigProvider } from '@ht/sprite-ui';
import zhCN from '@ht/sprite-ui/lib/locale/zh_CN';
import React, { type PropsWithChildren } from 'react';
import { useModel } from '@oula/oula';

const BasicLayout: React.FC<PropsWithChildren> = ({ children }) => {
  const [hostStoreState, dispatch] = useModel('useHost');
  const collapsed = hostStoreState?.global?.collapsed;
  return (
    <ConfigProvider prefixCls={BaseSettings.appName} locale={zhCN}>
      {/* <Button
        type="primary"
        onClick={() => {
          dispatch({
            type: 'global/changeLayoutCollapsed',
            payload: !collapsed,
          });
        }}
      >
        {collapsed ? '展开' : '折叠'}
      </Button> */}
      {children}
    </ConfigProvider>
  );
};

export default BasicLayout;
