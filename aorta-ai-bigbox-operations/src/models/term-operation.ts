import { getTerms } from '@/services/term-operation';
import { IResponseDataType } from './interface.d';

const TermOperationModel = {
  namespace: 'termOperation',
  state: {
    termList: [],
  },
  effects: {
    // 获取文档解析等页面需要的文档信息
    *getTerms({ payload }: any, { call, put }: any) {
      const response: IResponseDataType = yield call(getTerms, payload);
      yield put({
        type: 'getTermsSuccess',
        payload: response?.data || [],
      });
    },
  },
  reducers: {
    getTermsSuccess(state: any, { payload }: any) {
      return {
        ...state,
        termList: payload,
      };
    },
  },
};

export default TermOperationModel;
