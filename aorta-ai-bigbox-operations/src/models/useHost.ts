// 当子应用使用 useModel 的数据流方案时，以下代码将会建立子应用与主应用的数据通讯通道
import { getHost } from '@@/plugin-bigbox/host';
import { useEffect, useMemo, useState } from 'react';

/**
 * 主应用状态类型定义，为了在子应用开发时有更好的类型补全体验，这块需要根据主应用情况和子应用中使用到的状态进行必要的调整
 * **建议修改这部分的类型定义，匹配主应用的实际情况，以获得更好的开发时类型提示**
 */
interface HostStoreState {
  /**
   * 主应用 global model 中的 state
   */
  global: {
    collapsed: boolean;
  };
}

/**
 * 主应用暴露出来的 dva store dispatch 方法定义
 */
type Dispatch = (action: { type: string; payload?: unknown }) => void;

/**
 * 建立主应用全局 dva store 与子应用的数据通讯管道。子应用只需要将这个 hook 当做是一个能够修改主应用状态的 useReducer hook 即可
 *
 * **不建议修改 hook 函数内部的任意逻辑**
 */
export default function useHost() {
  const host = getHost();

  const [hostStoreState, setHostStoreState] = useState<Partial<HostStoreState>>(
    host?.getState() || {}
  );

  const hostDispatch = useMemo<Dispatch>(() => {
    return host?.dispatch;
  }, [host]);

  useEffect(() => {
    function syncHostState() {
      setHostStoreState(getHost()?.getState());
    }
    /**
     * 当 getHost 处于初始状态或主应用没有开启 dva 插件时，subscribe 为模拟函数，调用后的返回为 undefined
     * 当 getHost 已经获取了主应用的 dva store， subscribe 为 dva store 上的订阅函数，需要在组件卸载时进行取消订阅操作
     */
    const unsubscribe = getHost()?.subscribe(syncHostState) as any;

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [host]);

  return [
    /**
     * 主应用 dva store 中的全量 state，包含所有 model
     */
    hostStoreState,
    /**
     * 主应用 dva store 的 dispatch 方法
     */
    hostDispatch,
  ] as const;
}
