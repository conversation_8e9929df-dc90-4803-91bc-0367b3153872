/**
 * 动态主题demo
 * docs: http://spriteui.htsc/spriteui/docs/react/customize-theme-variable-cn
 *
 * 如何使用：
 *  yarn start:dyn 启动
 *
 * 注：使用动态主题时，自定义的颜色样式变量如（primary-color等）会与动态主题有冲突，建议注释掉颜色相关的less变量。
 */

import {
  Button,
  Checkbox,
  Col,
  ConfigProvider,
  Divider,
  Mentions,
  Radio,
  Row,
  Slider,
  Space,
  SpaceProps,
  Switch,
} from '@ht/sprite-ui';
import { useState } from 'react';
import { SketchPicker } from 'react-color';

import BaseSettings from '../../../config/BaseSettings';

const SplitSpace: React.FC<SpaceProps> = (props) => (
  <Space split={<Divider type="vertical" />} size={4} {...props} />
);

const DynamicTheme: React.FC = () => {
  const [color, setColor] = useState({
    primaryColor: '#255ff2',
    errorColor: '#ff4d4f',
    warningColor: '#faad14',
    successColor: '#52c41a',
    infoColor: '#255ff2',
  });

  const onColorChange = (nextColor: Partial<typeof color>) => {
    const mergedNextColor = {
      ...color,
      ...nextColor,
    };
    setColor(mergedNextColor);
    ConfigProvider.config({
      prefixCls: BaseSettings.appName,
      theme: mergedNextColor,
    });
  };

  return (
    <Row gutter={16} wrap={false}>
      <Col flex="none">
        <SketchPicker
          presetColors={['#255ff2', '#25b864', '#ff6f00']}
          color={color.primaryColor}
          onChange={({ hex }) => {
            onColorChange({
              primaryColor: hex,
            });
          }}
        />
      </Col>

      <Col flex="auto">
        <Space
          direction="vertical"
          split={<Divider />}
          style={{ width: '100%' }}
          size={0}
        >
          <h2>修改颜色，查看效果</h2>
          <SplitSpace>
            <Button type="primary">Primary</Button>
            <Button>Default</Button>
            <Button type="dashed">Dashed</Button>
            <Button type="text">Text</Button>
            <Button type="link">Link</Button>
          </SplitSpace>
          <SplitSpace>
            <Checkbox>Checkbox</Checkbox>

            <Radio.Group defaultValue="bamboo">
              <Radio value="bamboo">Bamboo</Radio>
              <Radio value="light">Light</Radio>
              <Radio value="little">Little</Radio>
            </Radio.Group>

            <Mentions placeholder="Mention by @">
              <Mentions.Option value="afc163">afc163</Mentions.Option>
              <Mentions.Option value="zombieJ">zombieJ</Mentions.Option>
              <Mentions.Option value="yesmeck">yesmeck</Mentions.Option>
            </Mentions>

            <Slider defaultValue={30} style={{ width: 100 }} />

            <Switch defaultChecked={true} />
          </SplitSpace>
        </Space>
      </Col>
    </Row>
  );
};

export default DynamicTheme;
