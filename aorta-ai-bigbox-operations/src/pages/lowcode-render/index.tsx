import React from 'react';
import LCRender from '@lowcode/lc-render';

/**
 * pc搭建帮助文档: https://xcg1a1l6ku.feishu.cn/wiki/wikcnZr00Kb76nltMdrLSptoGDf
 * lc-render文档: http://npm.htsc:8000/package/@lowcode/lc-render
 */
export default (props: any) => {
  return (
    <LCRender
      name="魔方pc搭建页面"
      target={props?.route?.target} // 结合路由配置传递渲染target
      // isDev={!location.hostname.startsWith('eip.htsc.com.cn')} // 开发模式下渲染搭建平台最新保存改动，生产模式下渲染发布改动
      // anyInfoHere={xxx} // 传递给搭建页面的数据,如token、用户信息等
    />
  );
};
