import { Space, Table, Tag } from '@ht/sprite-ui';
import type { ColumnsType } from '@ht/sprite-ui/es/table';
import { connect, Dispatch } from '@oula/oula';
import React, { useEffect } from 'react';

interface DataType {
  key: string;
  name: string;
  age: number;
  address: string;
  tags: string[];
}
interface TermOperationListProps {
  termList: any[];
  dispatch: Dispatch;
}

const TermOperationList: React.FC<TermOperationListProps> = (props) => {
  const { dispatch, termList } = props;

  console.log(termList);

  useEffect(() => {
    dispatch({
      type: 'termOperation/getTerms',
    });
  }, []);

  const columns: ColumnsType<DataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <a>{text}</a>,
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      render: (_, { tags }) => (
        <>
          {tags.map((tag) => {
            let color = tag.length > 5 ? 'geekblue' : 'green';
            if (tag === 'loser') {
              color = 'volcano';
            }
            return (
              <Tag color={color} key={tag}>
                {tag.toUpperCase()}
              </Tag>
            );
          })}
        </>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>Invite {record.name}</a>
          <a>Delete</a>
        </Space>
      ),
    },
  ];

  const data: DataType[] = [
    {
      key: '1',
      name: 'John Brown',
      age: 32,
      address: 'New York No. 1 Lake Park',
      tags: ['nice', 'developer'],
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address: 'London No. 1 Lake Park',
      tags: ['loser'],
    },
    {
      key: '3',
      name: 'Joe Black',
      age: 32,
      address: 'Sidney No. 1 Lake Park',
      tags: ['cool', 'teacher'],
    },
  ];
  return <Table columns={columns} dataSource={data} />;
};

// export default TermOperationList;

export default connect(({ termOperation, global, loading }: any) => ({
  termList: termOperation.termList,
  loading: loading.effects['termOperation/termList'],
}))(TermOperationList);
