{"name": "vue-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@types/node": "^22.15.17", "@vicons/ionicons5": "^0.13.0", "@vitejs/plugin-vue": "^5.2.4", "axios": "^1.9.0", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "vite-plugin-eslint": "^1.8.1", "vue-router": "^4.5.1"}}