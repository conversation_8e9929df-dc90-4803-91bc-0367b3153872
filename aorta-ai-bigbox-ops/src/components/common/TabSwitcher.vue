<template>
  <n-tabs 
    type="card" 
    :closable="tabs.length > 1"
    :value="currentTab"
    @update:value="handleTabUpdate"
    @close="handleCloseTab"
    style="margin-bottom: 20px; background: #fff; padding: 0px"
  >
    <n-tab 
      v-for="tab in tabs" 
      :key="tab.path"
      :name="tab.path"
    >
      <div style="display: flex; align-items: center">
        {{ tab.title }}
      </div>
    </n-tab>
  </n-tabs>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CloseCircleOutline } from '@vicons/ionicons5'

interface TabItem {
  title: string
  path: string
}

const router = useRouter()
const route = useRoute()

const tabs = ref<TabItem[]>([])

const currentTab = ref<string>('/term-operation')

// 监听路由变化处理tab切换
watch(() => route.fullPath, (newPath: string) => {
  // 如果当前路由有tabName且不存在相同路径或名称的tab，则添加新tab
  if (route.meta?.tabName) {
    const isDuplicate = tabs.value.some(tab => 
      tab.path === newPath || tab.title === route.meta.tabName
    )
    if (!isDuplicate) {
      tabs.value.push({
        title: route.meta.tabName,
        path: newPath
      })
    } else {
      console.log('Duplicate tab detected:', route.meta.tabName, newPath)
    }
  }
  // 切换到当前路由对应的tab
  if (tabs.value.some(tab => tab.path === newPath)) {
    currentTab.value = newPath
  }
}, { immediate: true })

const handleTabUpdate = (path: unknown) => {
  if (path === null || path === undefined) {
    console.warn('Received null or undefined path')
    return
  }
  
  let pathStr: string
  if (typeof path === 'string') {
    pathStr = path
  } else if (typeof path === 'number' || typeof path === 'symbol') {
    pathStr = String(path)
  } else {
    console.warn('Unsupported path type:', typeof path)
    return
  }
  
  handleTabChange(pathStr)
}

const handleTabChange = (path: string): void => {
  const tab = tabs.value.find(t => t.path === path)
  if (tab) {
    // 只有当当前不是该路由时才跳转，避免重复导航
    if (route.path !== path) {
      router.push(path)
    }
    currentTab.value = path
  }
}

const closeTab = (title: string) => {
  console.log("closeTab",title);
  const tab = tabs.value.find(t => t.title === title)
  if (!tab) {
    console.warn(`未找到标题为"${title}"的标签页`)
    return
  }
  handleCloseTab(tab.path)
}

const handleCloseTab = (path: string) => {
  // 当tab数量<=1时不允许关闭
  if (tabs.value.length <= 1) {
    console.warn('Cannot close the last remaining tab')
    return
  }

  const index = tabs.value.findIndex(tab => tab.path === path)
  console.log("handleCloseTab,index,path",index,path);
  if (index !== -1) {
    // 如果关闭的是当前激活的tab，则跳转到前一个tab
    if (currentTab.value === path) {
      const prevTab = tabs.value[index - 1] || tabs.value[index + 1]
      if (prevTab) {
        router.push(prevTab.path)
      }
    }
    tabs.value.splice(index, 1)
  }
}

// 导出方法供其他组件使用
defineExpose({
  closeTab
})
</script>
