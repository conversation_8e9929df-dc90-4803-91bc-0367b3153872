<template>
  <n-layout has-sider>
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :native-scrollbar="false"
      show-trigger
      :style="{
        '--n-color': '#000000',
        '--n-border-color': '#333333',
        height: '100vh'
      }"
    >
    <div class="system-title">
      大模型运营平台
    </div>
    <n-menu
      :options="menuOptions"
      :collapsed-width="64"
      :collapsed-icon-size="22"
      @update:value="handleMenuSelect"
      style="color: white"
      :theme-overrides="{
        itemTextColor: 'white',
        itemTextColorHover: 'white',
        itemTextColorActive: 'white',
        itemTextColorChildActive: 'white',
        itemColorHover: '#1890ff',
        itemColorActive: '#1890ff',
        itemIconColor: 'white',
        itemIconColorHover: 'white',
        itemIconColorActive: 'white',
        itemIconColorChildActive: 'white'
      }"
    />
    </n-layout-sider>
    
    <n-layout style="height: calc(100vh - 1px)">
      <n-layout-header bordered>
        <div class="tenant-selector">
          <n-dropdown
            trigger="click"
            :options="tenantOptions"
            @select="handleTenantChange"
          >
            <n-button>{{ currentTenant }}</n-button>
          </n-dropdown>
        </div>
      </n-layout-header>
      <n-layout-content content-style="padding: 5px 20px;">
        <div class="tab-switcher">
        <TabSwitcher ref="tabSwitcher" />
        </div>
        <router-view />
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script lang="ts" setup>
import { ref, h, provide, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { NLayout, NLayoutSider, NLayoutHeader, NLayoutContent, NMenu, NDropdown, NButton } from 'naive-ui'
import { BookOutline } from '@vicons/ionicons5'
import TabSwitcher from '@/components/common/TabSwitcher.vue'

const router = useRouter()
const tabSwitcher = ref<InstanceType<typeof TabSwitcher>>()
const currentTenant = ref('aorta (000045)')

// 确保TabSwitcher组件实例可用
// 直接provide TabSwitcher组件实例
provide('tabSwitcher', {
  closeTab: (title: string) => {
    console.log('Layout calling closeTab:', title)
    if (!tabSwitcher.value) {
      console.error('TabSwitcher component not found')
      return
    }
    tabSwitcher.value.closeTab(title)
  }
})

// 添加调试日志
onMounted(() => {
  console.log('TabSwitcher mounted:', tabSwitcher.value)
  if (!tabSwitcher.value) {
    console.error('TabSwitcher component not found')
  }
})

const tenantOptions = [
  { label: 'aorta (000045)', key: '000045' }
]

const menuOptions = [
  {
    label: () => h('span', { style: { color: 'white', fontWeight: 'bold' } }, '术语运营'),
    key: 'term-operation',
    icon: () => h(BookOutline, { color: 'white' }),
    children: [
      {
        label: () => h('span', { style: { color: 'white' } }, '术语列表'),
        key: 'term-operation-list'
      }
    ]
  },
    {
    label: () => h('span', { style: { color: 'white', fontWeight: 'bold' } }, '标准问运营'),
    key: 'standard-question',
    icon: () => h(BookOutline, { color: 'white' }),
    children: [
      {
        label: () => h('span', { style: { color: 'white' } }, '标准问列表'),
        key: 'standard-question-list'
      }
    ]
  }
]

function handleTenantChange(key: string) {
  currentTenant.value = tenantOptions.find(item => item.key === key)?.label || 'aorta (000045)'
}

function handleMenuSelect(key: string) {
  if (key === 'term-operation-list') {
    router.push('/term-operation')
  } else if (key === 'standard-question-list') {
    router.push('/standard-question/list')
  }
}
</script>

<style scoped>
.tenant-selector {
  height: 40px;
  margin: 10px 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.tab-switcher {
  margin: 0px;
}

.system-title {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333333;
}
</style>
