import { createRouter, createWebHashHistory } from 'vue-router';
import Layout from '@/layout/index.vue';
const routes = [
    {
        path: '/',
        component: Layout,
        redirect: '/term-operation',
        children: [
            {
                path: 'term-operation',
                name: 'TermOperation',
                component: () => import('@/views/term-operation/index.vue'),
                meta: {
                    title: '术语运营',
                    tabName: '术语运营列表'
                }
            },
            {
                path: 'term-operation/detail/:id',
                name: 'TermDetail',
                component: () => import('@/views/term-operation/detail.vue'),
                meta: {
                    title: '术语详情',
                    tabName: '术语运营详情'
                }
            },
            {
                path: 'term-operation/edit',
                name: 'TermCreate',
                component: () => import('@/views/term-operation/edit.vue'),
                meta: {
                    title: '新增术语',
                    tabName: '新增术语'
                }
            },
            {
                path: 'term-operation/edit/:id',
                name: 'TermEdit',
                component: () => import('@/views/term-operation/edit.vue'),
                meta: {
                    title: '术语编辑',
                    tabName: '术语运营编辑'
                }
            }
        ]
    },
    {
        path: '/standard-question/',
        component: Layout,
        children: [
            {
                path: 'list',
                name: 'StandardQuestion',
                component: () => import('@/views/standard-question/index.vue'),
                meta: {
                    title: '标准问',
                    tabName: '标准问列表'
                }
            },
            {
                path: 'detail/:id',
                name: 'QuestionDetail',
                component: () => import('@/views/standard-question/detail.vue'),
                meta: {
                    title: '标准问详情',
                    tabName: '标准问运营详情'
                }
            },
            {
                path: 'edit',
                name: 'QuestionCreate',
                component: () => import('@/views/standard-question/edit.vue'),
                meta: {
                    title: '新增标准问',
                    tabName: '新增标准问'
                }
            },
            {
                path: 'edit/:id',
                name: 'QuestionEdit',
                component: () => import('@/views/standard-question/edit.vue'),
                meta: {
                    title: '标准问编辑',
                    tabName: '标准问运营编辑'
                }
            }
        ]
    }
];
const router = createRouter({
    history: createWebHashHistory(),
    routes
});
export default router;
//# sourceMappingURL=index.js.map