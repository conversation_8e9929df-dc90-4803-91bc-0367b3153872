import { defineStore } from 'pinia'

interface TenantState {
  tenantId: string
}

interface TenantGetters {
  getTenantId: string
}

interface TenantActions {
  setTenantId(id: string): void
}

export const useTenantStore = defineStore('tenant', {
  state: (): TenantState => ({
    tenantId: '000045' // 默认租户ID
  }),
  getters: {
    getTenantId: (state) => state.tenantId
  },
  actions: {
    setTenantId(id: string) {
      this.tenantId = id
    }
  }
})
