import type { DefineComponent } from 'vue'

declare module 'naive-ui' {
  export const NButton: DefineComponent
  export const NCard: DefineComponent
  export const NConfigProvider: DefineComponent
  export const NDataTable: DefineComponent
  export const NDropdown: DefineComponent
  export const NForm: DefineComponent
  export const NFormItem: DefineComponent
  export const NInput: DefineComponent
  export const NLayout: DefineComponent
  export const NLayoutContent: DefineComponent
  export const NLayoutHeader: DefineComponent
  export const NLayoutSider: DefineComponent
  export const NMenu: DefineComponent
  export const NPagination: DefineComponent
  export const NSelect: DefineComponent
  export const NSpace: DefineComponent
  export const NTab: DefineComponent
  export const NTabs: DefineComponent<{
    value?: string
    'onUpdate:value'?: (value: string) => void
  }, {
    $emit: {
      (e: 'update:value', value: string): void
    }
  }>
  export const NTag: DefineComponent
  export const useMessage: () => any
}
