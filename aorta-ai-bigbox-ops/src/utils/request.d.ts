import { AxiosRequestConfig } from 'axios'

interface ResponseData<T = any> {
  data: T
  totalCount?: number
  currentPage?: number
  success: boolean
  msg?: string
}

interface TermDetail {
  id: number
  keyword: string
  description: string
  category: string
  useScenes: Array<{
    id: number
    sceneName: string
  }>
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  prompt: {
    summary: string | null
    prompt: string
    description: string | null
    promptType: string | null
  }
}

interface QuestionDetail {
  id: number
  question: string
  description: string
  category: string
  useScenes: Array<{
    id: number
    sceneName: string
  }>
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  prompt: {
    summary: string | null
    prompt: string
    description: string | null
    promptType: string | null
  }
}
export declare function getQuestions(params: {
  pageNum?: number
  pageSize?: number
  questionLike?: string
  useSceneNameList?: string[]
  category: string | null
}): Promise<ResponseData>

export declare function getTerms(params: {
  pageNum?: number
  pageSize?: number
  keywordLike?: string
  useSceneNameList?: string[]
  category: string | null
  useSceneIdList?: string[]
}): Promise<ResponseData>

export declare function getTermDetail(id: number): Promise<ResponseData<TermDetail>>

export declare function getQuestionDetail(id: number): Promise<ResponseData<QuestionDetail>>

export declare function deleteTermById(id: number): Promise<ResponseData>

export declare function deleteQuestionById(id: number): Promise<ResponseData>


export declare function saveKeyword(keywordObj: {
  id?: number
  tenantId: string
  keyword: string
  description: string
  category?: string
  parentCategory?: string
  enabled?: boolean
  prompt: {
    summary?: string | null
    prompt: string
    description?: string | null
    promptType?: string | null
  }
  useScenes: Array<{
    id: number | null
    sceneName: string
  }>
  createBy?: string
  updateBy?: string
}): Promise<ResponseData>


export declare function saveQuestion(questionObj: {
  id?: number
  tenantId: string
  question: string
  description: string
  category?: string
  parentCategory?: string
  enabled?: boolean
  prompt: {
    summary?: string | null
    prompt: string
    description?: string | null
    promptType?: string | null
  }
  useScenes: Array<{
    id: number | null
    sceneName: string
  }>
  createBy?: string
  updateBy?: string
}): Promise<ResponseData>
