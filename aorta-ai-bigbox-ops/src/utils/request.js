import axios from 'axios'

// 创建axios实例
const http = axios.create({
  baseURL: '/fspa',
  timeout: 5000
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    if (config.baseURL && config.url) {
      console.group('请求拦截器')
      console.log('请求方法:', config.method)
      console.log('请求路径:', config.url)
      console.log('完整URL:', config.baseURL + config.url)
      console.groupEnd()
    }
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    console.group('响应拦截器')
    console.log('响应状态:', response.status)
    console.log('原始响应数据:', response.data)
    
    // 统一处理响应数据
    const res = response.data || {}
    if (response.status === 200) {
      console.log('处理后的数据:', res)
      return res
    } else {
      const error = new Error(res.msg || '请求失败')
      console.error('业务错误:', error)
      return Promise.reject(error)
    }
  },
  error => {
    const errorMsg = error.response?.data?.msg || error.message
    console.error('响应拦截器错误详情:', {
      message: errorMsg,
      config: error.config,
      response: error.response?.data,
      stack: error.stack
    })
    return Promise.reject(error)
  }
)

// API方法
export const getQuestions = (params) => 
  http.post('/aorta-ai-operation/QuestionManageService/listQuestions', params)

export const getTerms = (params) => 
  http.post('/aorta-ai-operation/KeywordManageService/listKeywords', params)

export const getTermDetail = (id) => 
  http({
    method: 'post',
    url: '/aorta-ai-operation/KeywordManageService/queryKeywordById',
    data: id,
    headers: {
      'Content-Type': 'text/plain'
    }
  })

export const getQuestionDetail = (id) => 
  http({
    method: 'post',
    url: '/aorta-ai-operation/QuestionManageService/queryQuestionById',
    data: id,
    headers: {
      'Content-Type': 'text/plain'
    }
  })

export const deleteTermById = (id) => 
  http({
    method: 'post',
    url: '/aorta-ai-operation/KeywordManageService/deleteKeywordById',
    data: id,
    headers: {
      'Content-Type': 'text/plain'
    }
  }) 
  
export const deleteQuestionById = (id) => 
  http({
    method: 'post',
    url: '/aorta-ai-operation/QuestionManageService/deleteQuestionById',
    data: id,
    headers: {
      'Content-Type': 'text/plain'
    }
  })   

export const saveKeyword = (keywordObj) =>
  http.post('/aorta-ai-operation/KeywordManageService/saveKeywordObj', keywordObj)

export const saveQuestion = (questionObj) =>
  http.post('/aorta-ai-operation/QuestionManageService/saveQuestion', questionObj)
