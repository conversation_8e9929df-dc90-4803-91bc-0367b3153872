<template>
  <n-card title="标准问详情">
    <!-- 术语信息部分 -->
    <n-card title="问题信息" size="small" style="margin-bottom: 20px">
      <n-form :model="term" label-placement="left" label-width="auto">
        <n-form-item label="标准问">
          <n-input :value="term.question" disabled />
        </n-form-item>
        <n-form-item label="描述">
          <n-input :value="term.description" type="textarea" disabled />
        </n-form-item>
        <n-form-item label="分类">
          <n-input :value="term.category" disabled />
        </n-form-item>
        <n-form-item label="用途">
          <n-space>
            <n-tag v-for="scene in term.useScenes" :key="scene.id">{{ scene.name }}</n-tag>
          </n-space>
        </n-form-item>
        <n-form-item label="创建信息">
          <n-space>
            <n-input :value="term.createBy" disabled />
            <n-input :value="term.createTime" disabled />
          </n-space>
        </n-form-item>
        <n-form-item label="更新信息">
          <n-space>
            <n-input :value="term.updateBy" disabled />
            <n-input :value="term.updateTime" disabled />
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 提示信息部分 -->
    <n-card title="提示信息" size="small">
      <n-form :model="term.prompt" label-placement="left" label-width="auto">
        <n-form-item label="提示信息">
          <n-input :value="term.prompt.prompt" type="textarea" :rows="20" disabled />
        </n-form-item>
        <n-form-item label="提示摘要">
          <n-input :value="term.prompt.summary" type="textarea" disabled />
        </n-form-item>
        <n-form-item label="提示描述">
          <n-input :value="term.prompt.description" type="textarea" disabled />
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 关闭按钮 -->
    <div style="text-align: center; margin-top: 20px">
      <n-button type="primary" @click="handleClose">关闭</n-button>
    </div>
  </n-card>
</template>

<script lang="ts" setup>
import { ref, onMounted, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NCard, NForm, NFormItem, NInput, NSpace, NTag } from 'naive-ui'
import { getQuestionDetail } from '../../utils/request'

const tabSwitcher = inject<{closeTab: (title: string) => void}>('tabSwitcher')
if (!tabSwitcher) {
  console.error('tabSwitcher not provided')
}

const router = useRouter()
const route = useRoute()
const id = Number(route.params.id)

interface UseScene {
  id: string
  name: string
  sceneName?: string
}

interface Prompt {
  summary: string | null
  prompt: string
  description: string | null
}

interface QuestionDetail {
  id: number
  question: string
  description: string
  category?: string | null
  useScenes: UseScene[]
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  prompt: Prompt
}

const term = ref<QuestionDetail>({
  id: 0,
  question: '',
  description: '',
  category: null,
  useScenes: [],
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  prompt: {
    summary: null,
    prompt: '',
    description: null
  }
})

function handleClose() {
  // 使用inject获取的tabSwitcher
  if (tabSwitcher) {
    tabSwitcher.closeTab('术语运营详情')
  }
  router.back()
}

onMounted(async () => {
  if (id) {
    try {
      const res = await getQuestionDetail(id)
      console.log("res",res);
      if (res.success && res.data) {
        const data = res.data
        console.log('完整的API响应:', res)
        console.log('API数据对象:', data)
        console.log('question字段值:', data.question)
        console.log('当前term.value:', term.value)
        
        // 确保响应数据赋值给term.value
        // 确保响应式更新
        const newValue = {
          ...term.value,
          id: data.id,
          question: data.question || '',
          description: data.description || '',
          useScenes: data.useScenes?.map((scene: any) => ({
            id: scene.id,
            name: scene.sceneName || scene.name || ''
          })) || [],
          createBy: data.createBy || '',
          createTime: data.createTime ? new Date(data.createTime).toLocaleString() : '',
          updateBy: data.updateBy || '',
          updateTime: data.updateTime ? new Date(data.updateTime).toLocaleString() : '',
          prompt: {
            summary: data.prompt?.summary || null,
            prompt: data.prompt?.prompt || '',
            description: data.prompt?.description || null
          }
        }
        console.log('新赋值数据:', newValue)
        // 直接赋值整个对象确保响应式更新
        term.value = {
          id: data.id,
          question: data.question || '',
          description: data.description || '',
          category: data.category || null,
          useScenes: data.useScenes?.map((scene: any) => ({
            id: scene.id,
            name: scene.sceneName || scene.name || ''
          })) || [],
          createBy: data.createBy || '',
          createTime: data.createTime ? new Date(data.createTime).toLocaleString() : '',
          updateBy: data.updateBy || '',
          updateTime: data.updateTime ? new Date(data.updateTime).toLocaleString() : '',
          prompt: {
            summary: data.prompt?.summary || null,
            prompt: data.prompt?.prompt || '',
            description: data.prompt?.description || null
          }
        }
        console.log('最终term状态:', JSON.parse(JSON.stringify(term.value)))
      }
    } catch (error) {
      console.error('获取标准问详情失败:', error)
    }
  }
})
</script>
