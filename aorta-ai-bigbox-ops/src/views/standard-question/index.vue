<template>
  <n-card title="标准问">
    <n-form
      ref="formRef"
      :model="formValue"
      :label-placement="'left'"
      :style="{
        display: 'flex',
        flexWrap: 'nowrap',
        gap: '16px',
        overflowX: 'auto'
      }"
    >
      <n-form-item label="关键词" path="term" :show-label="false">
        <n-space style="display: flex; align-items: center; gap: 8px">
          <span style="flex: 0 0 60px; text-align: right">关键词</span>
          <n-input
            v-model:value="formValue.term"
            placeholder="请输入关键词"
            style="width: 100%"
          />
        </n-space>
      </n-form-item>
      <n-form-item label="分类" path="category" :show-label="false">
        <n-space style="display: flex; align-items: center; gap: 8px">
          <span style="flex: 0 0 60px; text-align: right">分类</span>
          <n-input
            v-model:value="formValue.category"
            placeholder="请输入分类"
            style="width: 200px"
          />
        </n-space>
      </n-form-item>
      <n-form-item label="用途" path="purpose" :show-label="false">
        <n-space style="display: flex; align-items: center; gap: 8px">
          <span style="flex: 0 0 60px; text-align: right">用途</span>
          <n-input
            v-model:value="formValue.purpose"
            placeholder="请输入用途"
            style="width: 200px"
          />
        </n-space>
      </n-form-item>
      <n-form-item>
        <n-button type="info" @click="handleSearch" style="width: 100%">查询</n-button>
      </n-form-item>
    </n-form>
    <div style="margin: 16px 0">
      <n-button type="primary" @click="handleAdd">
        <template #icon>
          <n-icon color="#fff"><Add /></n-icon>
        </template>
        新增标准问
      </n-button>
    </div>

    <n-data-table
      :columns="columns"
      :data="data"
      :loading="loading"
    />
    <div style="display: flex; justify-content: flex-end; margin-top: 16px">
      <n-pagination
        v-model:page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-count="pagination.pageCount"
        :item-count="pagination.itemCount"
        :page-sizes="[10, 20, 30, 40]"
        show-size-picker
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
  </n-card>
</template>

<script lang="ts" setup>
import { ref, reactive, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NCard, NDataTable, NForm, NFormItem, NInput, NSelect, NPagination, NTag, NSpace, NIcon, NTooltip } from 'naive-ui'
import { Add } from '@vicons/ionicons5'
import { getQuestions, deleteQuestionById } from '../../utils/request'

interface QuestionItem {
  id: number
  question: string
  description: string
  category: string
  parentCategory: string
  enabled: boolean
  prompt: {
    content?: string
    prompt?: string
    summary?: string
    description?: string
  }
  useScenes: Array<{
    id: string
    sceneName: string
  }>
  createTime: string
  createBy: string
  updateTime: string
  updateBy: string
}

const router = useRouter()
const formRef = ref<InstanceType<typeof NForm>>()
const loading = ref(false)
const message = useMessage()
const dialog = useDialog()

const formValue = reactive({
  category: null as string | null,
  purpose: null as string | null,
  term: ''
})


const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  itemCount: 0
})

const columns: DataTableColumns<QuestionItem> = [
  { title: '问题', key: 'question',    width: '300px', align: 'left',
        render: (row) => h(NTooltip, {
      trigger: 'hover',
      placement: 'top',
      style: 'max-width: 80vw; white-space: normal; word-break: break-word; padding: 8px;'
    }, {
      default: () => row.question ||'',
      trigger: () => h('div', {
        style: 'max-width: 300px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'
      }, row.question ||  '')
    })
   },
  { title: '分类', key: 'category', align: 'center' },
  // { 
  //   title: '提示语', 
  //   key: 'prompt',
  //   align: 'left',
  //   width: '300px',
  //   render: (row) => h(NTooltip, {
  //     trigger: 'hover',
  //     placement: 'top',
  //     style: 'max-width: 80vw; white-space: normal; word-break: break-word; padding: 8px;'
  //   }, {
  //     default: () => row.prompt.prompt || row.prompt.content || '',
  //     trigger: () => h('div', {
  //       style: 'max-width: 300px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'
  //     }, row.prompt.prompt || row.prompt.content || '')
  //   })
  // },
  {
    title: '用途',
    key: 'useScenes',
    align: 'center',
    width: '140px',
      render: (row) => h('div', { style: 'display: flex; flex-wrap: wrap; gap: 6px' },
      row.useScenes.map(scene => 
        h(NTag, { 
          type: 'info',
          bordered: false
        }, () => scene.sceneName)
      )
    )
  },
  { 
    title: '状态', 
    key: 'status',
    align: 'center',
    render: (row) => h(NTag, {
      type: row.enabled ? 'success' : 'default',
      bordered: false
    }, { 
      default: () => row.enabled ? '已发布' : '草稿' 
    })
  },
  { 
    title: '更新时间', 
    key: 'updateTime',
    align: 'center',
    width: '100px',
    render: (row) => {
      const date = new Date(row.updateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        // hour: '2-digit',
        // minute: '2-digit',
        // second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    }
  },
  // { title: '更新人', key: 'updateBy', align: 'center' },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    width: '200px',
    render: (rowData) => {
      const handleDelete = async () => {
        try {
          const res = await deleteQuestionById(rowData.id)
          if (res.success) {
            message.success('删除成功')
            handleSearch()
          } else {
            message.error(res.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除问题失败:', error)
          message.error('删除问题失败')
        }
      }
      
      return h('div', { style: 'display: flex; justify-content: center' }, 
        h(NSpace, { size: 'small' }, {
          default: () => [
            h(NButton, { 
              size: 'small',
              type:'info',
              onClick: () => handleView(rowData)
            }, { default: () => '查看' }),
            h(NButton, { 
              size: 'small',
              type:'warning',
              onClick: () => handleEdit(rowData) 
            }, { default: () => '编辑' }),
            h(NButton, {
              size: 'small',
              type: 'error',
              disabled: rowData.enabled,
              onClick: () => {
                dialog.warning({
                  title: '提示',
                  content: '确定要删除这条问题吗？',
                  positiveText: '确认',
                  negativeText: '取消',
                  onPositiveClick: () => {
                    handleDelete()
                    return true
                  },
                  onNegativeClick: () => {
                    return true
                  }
                })
              }
            }, { default: () => '删除' })
          ]
        })
      )
    }
  }
]

const data = ref<QuestionItem[]>([])

async function handleSearch() {
  loading.value = true
  try {
    try {
      const res = await getQuestions({
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
        questionLike: formValue.term,
        useSceneNameList: formValue.purpose ? [formValue.purpose.trim()] : undefined,
        category: formValue.category || null
      })
      console.log('API响应数据:', res)
      
      // 根据实际返回数据结构调整
      data.value = res.data || []
      pagination.itemCount = res.totalCount || 0
      pagination.pageCount = Math.ceil((res.totalCount || 0) / pagination.pageSize)
      console.log('表格数据:', data.value)
    } catch (error: unknown) {
      console.error('获取数据失败:', error)
const message = useMessage()
const dialog = useDialog()
      const errMsg = error instanceof Error ? error.message : '未知错误'
      message.error('获取数据失败: ' + errMsg)
    }
  } finally {
    loading.value = false
  }
}

function handlePageChange(page: number) {
  pagination.page = page
  handleSearch()
}

function handlePageSizeChange(pageSize: number) {
  pagination.pageSize = pageSize
  handleSearch()
}

function handleView(rowData: QuestionItem) {
  router.push(`/standard-question/detail/${rowData.id}`)
}

function handleEdit(rowData: QuestionItem) {
  router.push(`/standard-question/edit/${rowData.id}`)
}

function handleAdd() {
  router.push('/standard-question/edit')
}

onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
/* 分页样式已整合到n-data-table中 */
</style>
