<template>
  <n-card title="术语编辑">
    <!-- 术语信息部分 -->
    <n-card title="术语信息" size="small" style="margin-bottom: 20px">
      <n-form 
        ref="formRef"
        :model="term" 
        label-placement="left" 
        label-width="auto"
        :rules="rules"
        require-mark-placement="right"
        v-if="term"
      >
        <n-form-item label="术语" path="keyword" required>
          <n-input 
            v-model:value="term.keyword" 
            maxlength="50" 
            show-count
            placeholder="请输入术语名称"
          />
        </n-form-item>
        <n-form-item label="术语描述" path="description">
          <n-input 
            v-model:value="term.description" 
            type="textarea" 
            maxlength="1000"
            show-count
            placeholder="请输入术语描述"
          />
        </n-form-item>
        <n-form-item label="分类" path="category">
          <n-input 
            v-model:value="term.category" 
            maxlength="50"
            show-count
            placeholder="请输入分类"
          />
        </n-form-item>
        <n-form-item label="用途" path="useScenes">
          <n-dynamic-tags
            v-model:value="term.useScenes"
            :max="10"
            :render-tag="renderTag"
            @create="createTag"
          />
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 提示信息部分 -->
    <n-card title="提示信息" size="small">
      <n-form 
        ref="promptFormRef"
        :model="term.prompt" 
        label-placement="left" 
        label-width="auto"
        :rules="promptRules"
        require-mark-placement="right"
        v-if="term"
      >
        <n-form-item label="提示信息" path="prompt" required>
          <n-input 
            v-model:value="term.prompt.prompt" 
            type="textarea" 
            :rows="4"
            placeholder="请输入提示信息"
          />
        </n-form-item>
        <n-form-item label="提示摘要" path="summary">
          <n-input 
            v-model:value="term.prompt.summary" 
            type="textarea" 
            maxlength="100"
            show-count
            placeholder="请输入提示摘要"
          />
        </n-form-item>
        <n-form-item label="提示描述" path="description">
          <n-input 
            v-model:value="term.prompt.description" 
            type="textarea"
            placeholder="请输入提示描述"
          />
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 按钮区域 -->
    <div style="text-align: center; margin-top: 20px">
      <n-space justify="center">
        <n-button type="primary" @click="handleSave">保存</n-button>
        <n-button type="info" @click="handleSubmit">提交</n-button>
      </n-space>
    </div>
  </n-card>
</template>

<script lang="ts" setup>
import { ref, h, onMounted, watchEffect, inject } from 'vue'
import TabSwitcher from '@/components/common/TabSwitcher.vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NCard, NForm, NFormItem, NInput, NSpace, NTag, NDynamicTags, useMessage, type DynamicTagsOption } from 'naive-ui'
import { getTermDetail, saveKeyword } from '../../utils/request'
import { useTenantStore } from '../../stores/tenant'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const tabSwitcher = inject<{closeTab: (title: string) => void}>('tabSwitcher')
if (!tabSwitcher) {
  console.error('tabSwitcher not provided')
  // 不return，继续执行其他逻辑
}
const id = Number(route.params.id)

interface UseScene extends DynamicTagsOption {
  id?: number | null
  sceneName: string
  value: string
  label: string
}

interface Prompt {
  summary: string | null
  prompt: string
  description: string | null
  promptType: string | null
}

interface TermDetail {
  id: number | null
  keyword: string
  description: string
  category?: string | null
  useScenes: UseScene[]
  createBy: string
  createTime: string | null
  updateBy: string
  updateTime: string | null
  prompt: Prompt
  enabled?: boolean
  promptType?: string | null
}

const formRef = ref<InstanceType<typeof NForm>>()
const promptFormRef = ref<InstanceType<typeof NForm>>()
const loading = ref(true)

const rules = {
  keyword: [
    { required: true, message: '请输入术语名称', trigger: 'blur' },
    { max: 50, message: '术语名称不能超过50个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '术语描述不能超过1000个字符', trigger: 'blur' }
  ],
  category: [
    { max: 50, message: '分类不能超过50个字符', trigger: 'blur' }
  ]
}

const promptRules = {
  prompt: [
    { required: true, message: '请输入提示信息', trigger: 'blur' }
  ],
  summary: [
    { max: 100, message: '提示摘要不能超过100个字符', trigger: 'blur' }
  ]
}

const term = ref<TermDetail>({
  id: null,
  keyword: '',
  description: '',
  category: null,
  useScenes: [],
  createBy: '',
  createTime: null,
  updateBy: '',
  updateTime: null,
  enabled: false,
  promptType: 'text',
  prompt: {
    summary: null,
    prompt: '',
    description: null,
    promptType: 'text'
  }
})

// 确保数据加载完成
watchEffect(() => {
  if (term.value.keyword) {
    loading.value = false
  }
})

// 保存草稿
async function handleSave() {
  const tenantStore = useTenantStore()
  
  try {
    await formRef.value?.validate()
    await promptFormRef.value?.validate()
    
    const params = {
      ...term.value,
      tenantId: tenantStore.tenantId,
      prompt: {...term.value.prompt,promptType: term.value.prompt.promptType || 'text'},
          useScenes: term.value.useScenes.filter(scene => 
            scene && typeof scene === 'object' && scene.sceneName
          ).map(scene => ({
            id: scene.id || null,
            sceneName: scene.sceneName || '',
            value: scene.value || '',
            label: scene.label || ''
          })),
      category: term.value.category || null,
      enabled: term.value.enabled ?? false,
      createTime: null,
      updateTime: null
    }
    
    const res = await saveKeyword(params)
    console.log("保存结果：",res);
    if (res && res.success) {
      message.success('保存成功')
      if (res.data) {
        term.value.id = res.data
      }
    } else {
      message.error(res?.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存术语失败:', error)
    if (error instanceof Error) {
      message.error(error.message)
    }
  }
}

// 提交术语
async function handleSubmit() {
  const tenantStore = useTenantStore()
  
  try {
    await formRef.value?.validate()
    await promptFormRef.value?.validate()
    
    const params = {
      ...term.value,
      tenantId: tenantStore.tenantId,
      prompt: {...term.value.prompt,promptType: term.value.prompt.promptType || 'text'},
      useScenes: term.value.useScenes,
      category: term.value.category || null,
      promptType: 'text',
      enabled: true,
      createTime: null,
      updateTime: null
    }
    
    const res = await saveKeyword(params)
    if (res.success) {
      message.success('提交成功')
      console.log("closeTab,route.meta.tabName,tabSwitcher",route.meta.tabName,tabSwitcher);
      if (!tabSwitcher) {
        console.error('tabSwitcher is not available')
      } else if (!route.meta.tabName) {
        console.warn('No tabName found in route.meta')
      } else {
        console.log('Edit calling closeTab:', route.meta.tabName)
        tabSwitcher.closeTab(route.meta.tabName as string)
      }
      // 无论tabSwitcher是否存在都继续执行跳转
      router.push({
        path: '/term-operation',
        query: { refresh: Date.now() }
      })
    } else {
      message.error(res.msg || '提交失败')
    }
  } catch (error) {
    console.error('提交术语失败:', error)
    if (error instanceof Error) {
      message.error(error.message)
    }
  }
}

// 加载术语数据
async function loadTerm() {
  if (!id) return
  
  try {
    console.log('开始加载术语数据...')
    const res = await getTermDetail(id)
    console.log('获取到术语数据:', res)
    
    if (res.success && res.data) {
      const data = res.data
      console.log('原始数据:', data)
      
        const loadedTerm = {
          id: data.id,
          keyword: data.keyword || '',
          description: data.description || '',
          category: data.category || null,
          useScenes: data.useScenes?.map((scene: any) => ({
            id: scene.id,
            sceneName: scene.sceneName || scene.name || '',
            value: scene.id,
            label: scene.sceneName || scene.name || ''
          })) || [],
          createBy: data.createBy || '',
          createTime: null,
          updateBy: data.updateBy || '',
          updateTime: null ,
          promptType: 'text',
          enabled: false,
          prompt: {
            summary: data.prompt?.summary || null,
            prompt: data.prompt?.prompt || '',
            description: data.prompt?.description || null,
            promptType: data.prompt?.promptType || 'text'
          }
        }
      
      console.log('处理后数据:', loadedTerm)
      term.value = loadedTerm
      console.log('term更新完成:', term.value)
    }
  } catch (error) {
    console.error('获取术语详情失败:', error)
  }
}

const createTag = (label: string) => {
  // 返回兼容DynamicTagsOption的对象
  const newScene = {
    id: null,
    sceneName: label,
    value: label, // 使用label作为value
    label: label
  }
  term.value.useScenes.push(newScene)
  return newScene
}

const renderTag = (tag: string | UseScene, index: number) => {
  const sceneName = typeof tag === 'string' ? tag : tag?.sceneName || ''
  return h(NTag, {
    type: 'info',
    closable: true,
    onClose: () => {
      term.value.useScenes.splice(index, 1)
    }
  }, { default: () => sceneName })
}

// 确保数据加载完成后再渲染组件
onMounted(async () => {
  await loadTerm()
  console.log('Initial term data loaded:', term.value)
})
</script>
