/// <reference types="vite/client" />
/// <reference types="vite-plugin-pages/client" />
/// <reference types="naive-ui/volar" />
/// <reference types="alova" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'alova' {
  interface AlovaInstance {
    Get<T = any>(url: string, config?: AlovaRequestConfig): Promise<T>
  }

  interface AlovaRequestConfig {
    params?: Record<string, any>
    headers?: Record<string, string>
  }

  function createAlova(options: {
    baseURL?: string
    statesHook?: any
    requestAdapter?: any
    responded?: (response: any) => any
  }): AlovaInstance
}

declare module 'alova/vue' {
  export default function VueHook(): any
}

declare module '@alova/mock' {
  interface MockResponse {
    code: number
    data: any[]
    total: number
  }

  function defineMock(handlers: Record<string, (ctx: any) => MockResponse>): any
  function createAlovaMockAdapter(mock: any, options: { delay?: number }): any
}
