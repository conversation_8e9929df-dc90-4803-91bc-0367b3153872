{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "naive-ui/volar"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["src/**/*.js"], "references": [{"path": "./tsconfig.node.json"}]}