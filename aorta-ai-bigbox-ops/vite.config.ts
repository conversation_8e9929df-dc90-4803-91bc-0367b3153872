import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import eslint from 'vite-plugin-eslint'
import * as path from 'path'

export default defineConfig({
  base: '/fspa/aorta-ai-bigbox-ops/',
  plugins: [
    vue(),
    eslint({
      fix: true,
      include: ['src/**/*.vue', 'src/**/*.ts'],
      overrideConfigFile: path.resolve(__dirname, './.eslintrc.cjs')
    })
  ],
  server: {
    port: 5174,
    proxy: {
      '/fspa/aorta-ai-operation': {
        target: process.env.VITE_API_BASE_URL || 'http://168.63.65.40:8090',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fspa\/aorta-ai-operation/, '/aorta-ai-operation'),
        secure: false
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})
