/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/api/': {
      // 使用goapi 进行接口数据定义mock
      // http://goapi.htsc/goapi/web/#/home/<USER>/api?projectId=10002065&moduleid=129331
      target: 'http://mock.htsc/goapi/WEBFE/',
      changeOrigin: true,
    },
    '/fspa/aorta/dmz/api/': {
      target: 'http://168.63.84.88:8080/',
      changeOrigin: true,
    },
    '/fspa/aorta/ai/api/': {
      // target: 'http://10.102.70.41:8081/',
      target: 'http://168.64.33.185:80/',
      changeOrigin: true,
      // pathRewrite: { "/fspa/aorta/ai/api/": "/aorta/ai/api/" },
    },
    '/fspa/aorta/user/api/': {
      target: 'http://168.63.69.175:8081/',
      changeOrigin: true,
      pathRewrite: { "/fspa/aorta/user/api/": "/aorta/user/api/" }
    },
    '/fspa/aorta/operation/api/': {
      target: 'http://168.64.33.185:80/',
      changeOrigin: true,
    },
  }
};
