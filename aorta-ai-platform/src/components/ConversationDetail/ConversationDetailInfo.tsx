/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-22
 * @Description：会话详情-右侧会话详情
 */
import React, { useState } from 'react';
import _ from 'lodash';
import { Checkbox, Spin } from '@ht/sprite-ui';
import type { CheckboxValueType } from '@ht/sprite-ui/es/checkbox/Group';
import type { ConversationDetailsResult } from '@/services/types/api/dataFeedback/queryConversationDetails';
import AiChatMessage from '@/components/common/AiChatMessage';
import aiChatIcon from './images/aiChatIcon.png';

import styles from './conversationDetailInfo.less';

type ConversationDetailProps = {
  /** 会话详情 */
  conversationDetails: ConversationDetailsResult;
  /** 查询会话详情loading */
  queryConversationDetailsLoading: boolean;
  conversationDetailTypeChange: (checkedValues: CheckboxValueType[]) => void;
  submitOperationAnalysis: (query: any) => Promise<unknown>;
}
const ConversationDetailInfo: React.FC<ConversationDetailProps> = ({
  conversationDetails,
  queryConversationDetailsLoading,
  conversationDetailTypeChange,
  submitOperationAnalysis,
}) => {

  const options = [
    { label: '查看点赞回答', value: 'good' },
    { label: '查看点踩回答', value: 'bad' },
  ];

  const [checkedValues, setCheckedValues] = useState<any>('');

  const handleSubmitAnalysis = (submitParams:any) => {
    submitOperationAnalysis({ ...submitParams }).then((res:any) => {
      if (res.code === '0') {
        conversationDetailTypeChange(checkedValues);
      }
    });
  };

  return (
    <div className={styles.conversationDetailInfo}>
      <div className={styles.titleInfo}>
        <div className={styles.title}>会话详情</div>
        <Checkbox.Group options={options} onChange={(e) => { setCheckedValues(e); conversationDetailTypeChange(e); }} />
      </div>
      <div className={styles.aiChatMessage}>
        <div className={styles.aiChatHeader}>
          <img src={aiChatIcon} alt="" />
          <span className={styles.title}>问TA</span>
        </div>
        <div className={styles.aiChatContent}>
          {
            queryConversationDetailsLoading ? (
              <div className={styles.spinSty}>
                <Spin size='large' />
              </div>
            ) : (
              <AiChatMessage dataList={conversationDetails?.list}  onSubmitAnalysis={handleSubmitAnalysis} />
            )
          }
        </div>
      </div>
    </div>
  );
};

export default ConversationDetailInfo;
