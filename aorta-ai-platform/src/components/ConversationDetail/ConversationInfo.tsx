/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-22
 * @Description：会话详情-左侧会话信息
 */
import React from 'react';
import _ from 'lodash';
import type { ConversationInfoResult } from '@/services/types/api/dataFeedback/queryConversationInfo';

import styles from './conversationInfo.less';

type ConversationInfoProps = {
  /** 会话信息 */
  conversationInfo: ConversationInfoResult;
}
const ConversationInfo: React.FC<ConversationInfoProps> = ({
  conversationInfo,
}) => {
  const conversationInfoArr = [
    { label: '会话ID：', value: conversationInfo?.conversationId || '' },
    { label: '提问人：', value: `${conversationInfo?.questioner || ''} ${conversationInfo?.department || ''}` },
    { label: '员工类型', value: conversationInfo?.tgType || '--' },
    { label: '首次提问时间：', value: conversationInfo?.questionTime || '' },
    { label: '末次提问时间：', value: conversationInfo?.updateTime || '' },
    { label: '问题数：', value: conversationInfo?.questionNum },
    { label: '子问题数：', value: conversationInfo?.subQuestionNum },
    { label: '点赞数：', value: conversationInfo?.likesNum },
    { label: '点踩数：', value: conversationInfo?.dislikesNum },
  ];

  return (
    <div className={styles.conversationInfo}>
      <div className={styles.title}>会话信息</div>
      {
        _.map(conversationInfoArr, (item) => {
          return (
            <div className={styles.conversationInfoItem} key={item.label}>
              <span className={styles.label}>{item.label}</span>
              <span className={styles.value}>{item.value}</span>
            </div>
          );
        })
      }
    </div>
  );
};

export default ConversationInfo;
