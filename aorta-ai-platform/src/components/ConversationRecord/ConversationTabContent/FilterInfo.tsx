/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-会话列表筛选条件信息
 */
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import moment from 'moment';
import { DatePicker, Space, Select } from '@ht/sprite-ui';
import type { FeedbackPersonResult, IQueryFeedbackPerson } from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import Button from '@/components/common/Button';
import DepartmentTreeSelect from '@/components/DataFeedback/DepartmentTreeSelect';
import FilterCascader from '@/components/KnowledgeWarehouseManagement/FileManagement/FilterCascader';

import { conversationRecordPersistenceKey } from '@/pages/conversationRecord/config';
import styles from './filterInfo.less';

const { RangePicker } = DatePicker;

type FilterInfoProps = {
  /** 提问人 */
  questioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  queryQuestionerLoading: boolean;
  /** 查询提问人(同查询反馈人) */
  queryQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
};

const getMatchingValues = (initVal:string[]) => {
  const newArr: string[][] = [];
  if (initVal?.length) {
    initVal.forEach(item => {
      newArr.push([item]);
    });
  }
  return newArr;
};

const getMatchingLabels = (values: string[], data: {label:string, value:string}[]) => {

  if (values?.length) {
    // 1. 过滤出value在values数组中的对象
    const matchingItems = data.filter(item => values?.includes(item.value));

    // 2. 提取label属性
    const labels = matchingItems.map(item => item.label);

    // 3. 用逗号拼接所有标签
    return labels.join(', ');
  }
  return '不限';
};
const FilterInfo: React.FC<FilterInfoProps> = ({
  questioner,
  department,
  dictData,
  queryQuestionerLoading,
  queryQuestioner,
  filterInfoChange,
  handleQueryData,
}) => {

  const persistenceKey = conversationRecordPersistenceKey.conversationKey;
  const storageValue = window.sessionStorage.getItem(persistenceKey);
  const sessionParams = storageValue ? JSON.parse(storageValue) : {};
  console.log('sessionParams', sessionParams);

  // 时间范围
  const defaultTimeRange = sessionParams?.filterParams?.startTime &&
      [moment(sessionParams?.filterParams?.startTime), moment(sessionParams?.filterParams?.endTime)];

  // 提问人
  const defaultQuestionerValue =  sessionParams?.filterParams?.empId;

  // 所属部门
  const defaultFeedbackDepartmentValue = _.isEmpty(sessionParams?.filterParams?.orgList) ?
    null : sessionParams?.filterParams?.orgList;

  //  员工类型
  const  initEmpTypeLabel = useRef('不限');
  const initEmpTypeValue = getMatchingValues(sessionParams?.filterParams?.tgType);

  // 提问人
  const [questionerValue, setQuestionerValue] = useState<string>(defaultQuestionerValue  || '不限');

  // 员工类型
  const [empTypeLabel, setEmpTypeLabel] = useState<string>(initEmpTypeLabel.current);

  useEffect(() => {
    if (defaultQuestionerValue) {
      questionerSearch(defaultQuestionerValue);
    }
  }, [defaultQuestionerValue]);

  useEffect(() => {
    if (dictData?.tgTypeDict) {
      initEmpTypeLabel.current = getMatchingLabels(sessionParams?.filterParams?.tgType, dictData?.tgTypeDict);
      setEmpTypeLabel(initEmpTypeLabel.current);
    }
  }, [dictData?.tgTypeDict]);

  // 提问人下拉数据
  const questionerOptions = _.map(questioner?.dataList, item => {
    return {
      label: `${item?.userName}(${item?.userId})`,
      value: item?.userId,
    };
  });

  // 提问人搜索
  const questionerSearch = (value: string) => {
    queryQuestioner({ keyword: value, pageSize: 10, pageNum: 1 });
  };

  // 提问人选择框change
  const questionerChange = (value: string) => {
    if (value) {
      filterInfoChange('empId', value);
    } else {
      filterInfoChange('empId', '');
      queryQuestioner({ keyword: '', pageSize: 10, pageNum: 1 });
    }
    setQuestionerValue(value ? value : '不限');
  };

  // 提问人展开下拉菜单的回调
  const questionerVisibleChange = (visible: boolean) => {
    if (visible && questionerValue !== '不限' && _.size(questionerOptions) !== 1) {
      queryQuestioner({ keyword: questionerValue, pageSize: 10, pageNum: 1 });
    }
  };

  const handleDepartmentChange = (valueObj:any) => {
    filterInfoChange('orgList', valueObj);
  };

  // 处理显示文本
  const handleDisplayText = (selectedOptions: any[]) => {
    let cascaderDisplayText = '';
    _.forEach(selectedOptions, (selectedOption: any[], index: number) => {
      const labels = _.map(selectedOption, (item: { label: string; }) => item.label);
      cascaderDisplayText += labels.join('-');
      if (index !== selectedOptions.length - 1) {
        cascaderDisplayText += '，';
      }
    });
    return cascaderDisplayText;
  };

  const filterCascaderChange = (fieldName: string, value: (string | number)[][], selectedOptions: any, setLabel:any) => {
    filterInfoChange(fieldName, value);
    if (_.isEmpty(selectedOptions)) {
      setLabel('不限');
      return;
    }
    setLabel(handleDisplayText(selectedOptions));
  };

  return (
    <div className={styles.filterInfo}>
      <Space size={32} className={styles.spaceSty} wrap={true}>
        <div className={styles.timeRange}>
          <span>首次提问时间：</span>
          <RangePicker
            defaultValue={defaultTimeRange || []}
            placeholder={['开始时间', '结束时间']}
            className={styles.datePicker}
            onChange={
              (_, dateStrings: [string, string]) => filterInfoChange('timeRange', dateStrings)
            }
          />
        </div>
        <div className={styles.questioner}>
          <Select
            isRemoteSearch={true}
            showSearch={true}
            bordered={false}
            allowClear={questionerValue !== '不限'}
            value={questionerValue}
            options={questionerOptions}
            defaultActiveFirstOption={false}
            loading={queryQuestionerLoading}
            className={styles.questionerSelect}
            onSearch={questionerSearch}
            onChange={questionerChange}
            onDropdownVisibleChange={questionerVisibleChange}
          />
        </div>
        <div>
          <DepartmentTreeSelect
            defaultValue={defaultFeedbackDepartmentValue}
            treeData={department}
            filterInfoChange={filterInfoChange}
            onDepartmentChange={handleDepartmentChange}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={initEmpTypeValue}
            options={dictData?.tgTypeDict}
            fieldName="员工类型："
            cascaderLabel={empTypeLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('tgType', value, selectOptions, setEmpTypeLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
      </Space>
      <Button type="primary" onClick={handleQueryData}>查询</Button>
    </div>
  );
};

export default FilterInfo;
