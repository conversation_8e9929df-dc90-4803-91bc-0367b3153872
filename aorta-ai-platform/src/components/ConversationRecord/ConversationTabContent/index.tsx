/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-会话维度Tab内容
 */
import React from 'react';
import _ from 'lodash';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type { FeedbackPersonResult, IQueryFeedbackPerson } from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { ConversationList } from '@/services/types/api/conversationRecord/queryConversationList';
import type { ConversationStaticsResult } from '@/services/types/api/conversationRecord/queryConversationStatics';
import FilterInfo from './FilterInfo';
import CountInfo from './CountInfo';
import TableList from './TableList';

type ConversationTabContentProps = {
  /** 会话维度提问人 */
  conversationQuestioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  // 会话维度列表
  conversationList: ConversationList;
  // 会话维度的统计信息
  conversationStatics: ConversationStaticsResult,
  queryConversationListLoading: boolean;
  queryConversationQuestionerLoading: boolean;
  /** 查询会话维度提问人(同查询反馈人) */
  queryConversationQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
};
const ConversationTabContent: React.FC<ConversationTabContentProps> = ({
  conversationQuestioner,
  department,
  dictData,
  conversationList,
  conversationStatics,
  queryConversationListLoading,
  queryConversationQuestionerLoading,
  queryConversationQuestioner,
  filterInfoChange,
  handleQueryData,
  handleDetail,
  handleTableChange,
}) => {

  return (
    <>
      {/* 筛选条件信息 */}
      <FilterInfo
        questioner={conversationQuestioner}
        department={department}
        dictData={dictData}
        queryQuestionerLoading={queryConversationQuestionerLoading}
        queryQuestioner={queryConversationQuestioner}
        filterInfoChange={filterInfoChange}
        handleQueryData={handleQueryData}
      />
      {/* 统计信息 */}
      <CountInfo conversationStatics={conversationStatics} />
      {/* 列表 */}
      <TableList
        conversationList={conversationList}
        loading={queryConversationListLoading}
        handleDetail={handleDetail}
        handleTableChange={handleTableChange}
      />
    </>
  );
};

export default ConversationTabContent;
