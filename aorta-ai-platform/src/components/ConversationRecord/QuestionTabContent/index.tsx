/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-问题维度Tab内容
 */
import React from 'react';
import _ from 'lodash';
import type { FeedbackPersonResult, IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { QuestionList } from '@/services/types/api/conversationRecord/queryQuestionList';
import type { QuestionStaticsResult } from '@/services/types/api/conversationRecord/queryQuestionStatics';
import FilterInfo from './FilterInfo';
import CountInfo from './CountInfo';
import TableList from './TableList';

type QuestionTabContentProps = {
  /** 问题维度提问人 */
  questionQuestioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  // 会话维度的统计信息
  questionStatics: QuestionStaticsResult,
  questionList: QuestionList;
  queryQuestionListLoading: boolean;
  queryQuestionQuestionerLoading: boolean;
  /** 查询问题维度提问人(同查询反馈人) */
  queryQuestionQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
  handleDownloadData: () => void;
  handleTraceId: (traceId: string) => void;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
};
const QuestionTabContent: React.FC<QuestionTabContentProps> = ({
  questionQuestioner,
  department,
  dictData,
  questionList,
  questionStatics,
  queryQuestionListLoading,
  queryQuestionQuestionerLoading,
  queryQuestionQuestioner,
  filterInfoChange,
  handleQueryData,
  handleDownloadData,
  handleTraceId,
  handleDetail,
  handleTableChange,
}) => {

  return (
    <>
      {/* 筛选条件信息 */}
      <FilterInfo
        questioner={questionQuestioner}
        department={department}
        dictData={dictData}
        queryQuestionerLoading={queryQuestionQuestionerLoading}
        queryQuestioner={queryQuestionQuestioner}
        filterInfoChange={filterInfoChange}
        handleQueryData={handleQueryData}
        handleDownloadData={handleDownloadData}
      />
      {/* 统计信息 */}
      <CountInfo questionStatics={questionStatics} />
      {/* 列表 */}
      <TableList
        questionList={questionList}
        loading={queryQuestionListLoading}
        handleTraceId={handleTraceId}
        handleDetail={handleDetail}
        handleTableChange={handleTableChange}
      />
    </>
  );
};

export default QuestionTabContent;
