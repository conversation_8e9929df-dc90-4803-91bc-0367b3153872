/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-20
 * @Description：会话记录-问题维度列表
 */
import React from 'react';
import _ from 'lodash';
import Button from '@/components/common/Button';
import { Space, Table, Tooltip } from '@ht/sprite-ui';
import type { ColumnsType } from '@ht/sprite-ui/lib/table';
import type {
  QuestionList,
  QuestionListItem,
} from '@/services/types/api/conversationRecord/queryQuestionList';

import styles from './tableList.less';

type FileTableProps = {
  questionList: QuestionList;
  loading: boolean;
  handleTraceId: (traceId: string) => void;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
};
const TableList: React.FC<FileTableProps> = ({
  questionList,
  loading,
  handleTraceId,
  handleDetail,
  handleTableChange,
}) => {

  // pagination对象
  const paginationProps = {
    showSizeChanger: true,
    pageSize: questionList?.page?.pageSize || 20,
    current: questionList?.page?.pageNum || 1,
    total: questionList?.page?.totalCount || 0,
  };

  // 问题详情columns
  const questionDetailRender = (text: string, record: QuestionListItem) => {
    return (
      <Tooltip placement='topLeft' title={text}>
        {text}
      </Tooltip>
    );
  };

  // traceIDcolumns
  const traceIdColumnsRender = (text: string, record: QuestionListItem) => {
    return (
      <Tooltip placement='topLeft' title={text}>
        <a onClick={() => handleTraceId(record.traceId)}>{text}</a>
      </Tooltip>
    );
  };

  // 会话详情columns
  const actionColumnsRender = (text: string, record: QuestionListItem) => {
    let empId = '';
    if (record.questioner) {
      empId = record?.questioner?.split(' ')?.[0] || record.questioner;
    }
    return (
      <Space size="middle" className={styles.actionRender}>
        <Button
          type="link"
          onClick={() => handleDetail(record.conversationId, empId)}
        >
          查看
        </Button>
      </Space>
    );
  };
  const columns: ColumnsType<QuestionListItem> = [
    {
      title: '提问人',
      dataIndex: 'questioner',
      width: 280,
    },
    {
      title: '所属分支/部门',
      width: 320,
      dataIndex: 'department',
    },
    {
      title: '员工类型',
      width: 320,
      dataIndex: 'tgType',
      render: (text) => {
        return text || '--';
      },
    },
    {
      title: '子问题详情',
      width: 320,
      dataIndex: 'questionDetail',
      ellipsis: {
        showTitle: false,
      },
      render: questionDetailRender,
    },
    {
      title: '提问时间',
      width: 220,
      dataIndex: 'questionTime',
      sorter: true,
      defaultSortOrder: 'descend',
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: '场景覆盖',
      width: 120,
      dataIndex: 'scenarioCoverage',
    },
    {
      title: '涉及Agent',
      width: 180,
      dataIndex: 'agent',
    },
    {
      title: '答案来源',
      width: 180,
      dataIndex: 'answerSource',
    },
    {
      title: 'traceID',
      width: 210,
      dataIndex: 'traceId',
      ellipsis: {
        showTitle: false,
      },
      render: traceIdColumnsRender,
    },
    {
      title: '会话详情',
      key: 'action',
      width: 110,
      render: actionColumnsRender,
    },
  ];

  return (
    <div className={styles.tableList}>
      <Table
        rowKey={(record) => { return record?.id ; }}
        columns={columns}
        dataSource={questionList.list}
        pagination={paginationProps}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default TableList;
