/**
 * @Author：lijialong<K0171607>
 * @Date：2025-07-30
 * @Description：会话记录-子问题维度Tab内容
 */
import React from 'react';
import _ from 'lodash';
import type { FeedbackPersonResult, IQueryFeedbackPerson,
} from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { QuestionList } from '@/services/types/api/conversationRecord/queryQuestionList';
import type { QuestionStaticsResult } from '@/services/types/api/conversationRecord/queryQuestionStatics';
import FilterInfo from './FilterInfo';
import CountInfo from './CountInfo';
import TableList from './TableList';

type QuestionTabContentProps = {
  /** 问题维度提问人 */
  questionQuestioner: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 答案来源字典 */
  dictAnsourceData:DictResult;
  // 会话维度的统计信息
  subQuestionStatics: QuestionStaticsResult,
  subQuestionList: QuestionList;
  querySubQuestionListLoading: boolean;
  queryQuestionQuestionerLoading: boolean;
  /** 查询问题维度提问人(同查询反馈人) */
  queryQuestionQuestioner: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
  handleDownloadData: () => void;
  handleTraceId: (traceId: string) => void;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
  subLoading: boolean;
};
const SubQuestionTabContent: React.FC<QuestionTabContentProps> = ({
  questionQuestioner,
  department,
  dictData,
  dictAnsourceData,
  subQuestionList,
  subQuestionStatics,
  querySubQuestionListLoading,
  queryQuestionQuestionerLoading,
  queryQuestionQuestioner,
  filterInfoChange,
  handleQueryData,
  handleDownloadData,
  handleTraceId,
  handleDetail,
  handleTableChange,
  subLoading,
}) => {

  return (
    <>
      {/* 筛选条件信息 */}
      <FilterInfo
        questioner={questionQuestioner}
        department={department}
        dictData={dictData}
        queryQuestionerLoading={queryQuestionQuestionerLoading}
        queryQuestioner={queryQuestionQuestioner}
        filterInfoChange={filterInfoChange}
        handleQueryData={handleQueryData}
        handleDownloadData={handleDownloadData}
        dictAnsourceData={dictAnsourceData}
        subLoading={subLoading}
      />
      {/* 统计信息 */}
      <CountInfo questionStatics={subQuestionStatics} />
      {/* 列表 */}
      <TableList
        questionList={subQuestionList}
        loading={querySubQuestionListLoading}
        handleTraceId={handleTraceId}
        handleDetail={handleDetail}
        handleTableChange={handleTableChange}
      />
    </>
  );
};

export default SubQuestionTabContent;
