/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-14
 * @Description：数据反馈-列表筛选条件信息
 */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { DatePicker, Space, Select, TreeSelect } from '@ht/sprite-ui';
import type { FeedbackPersonResult, IQueryFeedbackPerson } from '@/services/types/api/dataFeedback/queryFeedbackPerson';
import type { DepartmentInfoItem } from '@/services/types/api/dataFeedback/queryDepartmentInfo';
import type { DictResult } from '@/services/types/api/dataFeedback/queryFeedbackTagsAndAgentDict';
import Button from '../common/Button';
import FilterCascader from '../KnowledgeWarehouseManagement/FileManagement/FilterCascader';
import DepartmentTreeSelect from './DepartmentTreeSelect';
import { FeedbackChannelsEnum, SceneCoverageEnum, IsAnalysisEnum } from './config';

import styles from './filterInfo.less';
import { dataFeedbackPersistenceKey } from '@/pages/dataFeedback/config';
import moment from 'moment';

const { SHOW_ALL } = TreeSelect;
const { RangePicker } = DatePicker;

type FilterInfoProps = {
  /** 数据类型 */
  dataType: string;
  /** 反馈人 */
  feedbackPerson: FeedbackPersonResult;
  /** 所属部门 */
  department: DepartmentInfoItem[];
  /** 反馈标签和Agent字典 */
  dictData: DictResult;
  /** 查询反馈人loading */
  queryFeedbackPersonLoading: boolean;
  /** 查询反馈人 */
  queryFeedbackPerson: (query: IQueryFeedbackPerson['Query']) => Promise<unknown>;
  filterInfoChange: (filterFieldName: string, value: any) => void;
  handleQueryData: () => void;
};

const getMatchingValues = (initVal: string[]) => {
  const newArr: string[][] = [];
  if (initVal?.length) {
    initVal.forEach(item => {
      newArr.push([item]);
    });
  }
  return newArr;
};

const getMatchingLabels = (values: string[], data: { label: string, value: string }[]) => {
  if (values?.length) {
    // 1. 过滤出value在values数组中的对象
    const matchingItems = data.filter(item => values?.includes(item.value));

    // 2. 提取label属性
    const labels = matchingItems.map(item => item.label);

    // 3. 用逗号拼接所有标签
    return labels.join(', ');
  }
  return '不限';
};

const FilterInfo: React.FC<FilterInfoProps> = ({
  dataType,
  feedbackPerson,
  department,
  dictData,
  queryFeedbackPersonLoading,
  queryFeedbackPerson,
  filterInfoChange,
  handleQueryData,
}) => {

  const persistenceKey = dataFeedbackPersistenceKey.badKey;
  const storageValue = window.sessionStorage.getItem(persistenceKey);
  const sessionParams = storageValue ? JSON.parse(storageValue) : {};

  // 时间范围
  const defaultTimeRange = sessionParams?.filterParams?.startTime &&
    [moment(sessionParams?.filterParams?.startTime), moment(sessionParams?.filterParams?.endTime)];

  // 反馈人
  const initFeedbackPersonValue = sessionParams?.filterParams?.empId;

  // 所属部门
  const defaultFeedbackDepartmentValue = _.isEmpty(sessionParams?.filterParams?.orgList) ?
    null : sessionParams?.filterParams?.orgList;

  // 反馈标签
  let initFeedbackTagsLabel = '不限';
  const initFeedbackTagsValue = getMatchingValues(sessionParams?.filterParams?.feedbackTags);

  // 反馈渠道
  const initFeedbackChannelLabel = getMatchingLabels(
    sessionParams?.filterParams?.feedbackChannels, FeedbackChannelsEnum);
  const initFeedbackChannelValue = getMatchingValues(sessionParams?.filterParams?.feedbackChannels);

  //  场景覆盖
  const initSceneCoverageLabel = getMatchingLabels(sessionParams?.filterParams?.sceneCoverages, SceneCoverageEnum);
  const initSceneCoverageValue = getMatchingValues(sessionParams?.filterParams?.sceneCoverages);

  // 涉及agent
  let initAgentsLabel = '不限';
  const initAgentsValue = getMatchingValues(sessionParams?.filterParams?.agents);

  // 是否分析
  const initIsAnalysisLabel = getMatchingLabels(
    sessionParams?.filterParams?.isAnalysis, IsAnalysisEnum);
  const initIsAnalysisValue = getMatchingValues(sessionParams?.filterParams?.isAnalysis);

  //  员工类型
  let initEmpTypeLabel = getMatchingLabels(sessionParams?.filterParams?.tgType, dictData?.tgTypeDict);
  const initEmpTypeValue = getMatchingValues(sessionParams?.filterParams?.tgType);

  const [feedbackPersonValue, setFeedbackPersonValue] = useState<string>(initFeedbackPersonValue || '不限');

  const [feedbackTagsLabel, setFeedbackTagsLabel] = useState<string>(initFeedbackTagsLabel);
  const [defaultFeedbackTagsValue, setDefaultFeedbackTagsValue] = useState<any>(initFeedbackTagsValue);

  const [feedbackChannelsLabel, setFeedbackChannelsLabel] = useState<string>(initFeedbackChannelLabel);
  const [defaultFeedbackChannelValue, setDefaultFeedbackChannelValue] = useState<any>(initFeedbackChannelValue);

  const [sceneCoverageLabel, setSceneCoverageLabel] = useState<string>(initSceneCoverageLabel);
  const [defaultSceneCoverageValue, setDefaultSceneCoverageValue] = useState<any>(initSceneCoverageValue);

  const [agentsLabel, setAgentsLabel] = useState<string>(initAgentsLabel);
  const [defaultAgentsValue, setDefaultAgentsValue] = useState<any>(initAgentsValue);

  const [isAnalysisLabel, setIsAnalysisLabel] = useState<string>(initIsAnalysisLabel);
  const [defaultAnalysisValue, setDefaultAnalysisValue] = useState<any>(initIsAnalysisValue);

  const [empTypeLabel, setEmpTypeLabel] = useState<string>(initEmpTypeLabel);
  const [defaultEmpTypeValue, setDefaultEmpTypeValue] = useState<any>(initEmpTypeValue);

  useEffect(() => {
    if (initFeedbackPersonValue) {
      feedbackPersonSearch(initFeedbackPersonValue);
    }
  }, [initFeedbackPersonValue]);

  useEffect(() => {
    if (dictData?.tagDict) {
      initFeedbackTagsLabel = getMatchingLabels(sessionParams?.filterParams?.feedbackTags, dictData?.tagDict);
      setFeedbackTagsLabel(initFeedbackTagsLabel);
      setDefaultFeedbackTagsValue(initFeedbackTagsValue);
    }
  }, [dictData?.tagDict]);

  useEffect(() => {
    if (dictData?.agentDict) {
      initAgentsLabel = getMatchingLabels(sessionParams?.filterParams?.agents, dictData?.agentDict);
      setAgentsLabel(initAgentsLabel);
      setDefaultAgentsValue(initAgentsValue);
    }
  }, [dictData?.agentDict]);

  useEffect(() => {
    if (dictData?.tgTypeDict) {
      initEmpTypeLabel = getMatchingLabels(sessionParams?.filterParams?.tgType, dictData?.tgTypeDict);
      setEmpTypeLabel(initEmpTypeLabel);
      setDefaultEmpTypeValue(initEmpTypeValue);
    }
  }, [dictData?.tgTypeDict]);

  useEffect(() => {
    // setFeedbackTagsLabel('不限');
    if (dataType === 'likes') {
      setFeedbackTagsLabel('不限');
      setDefaultFeedbackTagsValue(undefined);
    }
    // setFeedbackTagsLabel(initFeedbackTagsLabel);
    // setDefaultFeedbackTagsValue(initFeedbackTagsValue);

    setFeedbackChannelsLabel(initFeedbackChannelLabel);
    setDefaultFeedbackChannelValue(initFeedbackChannelValue);

    setIsAnalysisLabel(initIsAnalysisLabel);
    setDefaultAnalysisValue(initIsAnalysisValue);

    setEmpTypeLabel(initEmpTypeLabel);
    setDefaultEmpTypeValue(initEmpTypeValue);

    // setAgentsLabel(initAgentsLabel);
    // setDefaultAgentsValue(initAgentsValue);

  }, [dataType]);

  // 处理显示文本
  const handleDisplayText = (selectedOptions: any[]) => {
    let cascaderDisplayText = '';
    _.forEach(selectedOptions, (selectedOption: any[], index: number) => {
      const labels = _.map(selectedOption, (item: { label: string; }) => item.label);
      cascaderDisplayText += labels.join('-');
      if (index !== selectedOptions.length - 1) {
        cascaderDisplayText += '，';
      }
    });
    return cascaderDisplayText;
  };

  // FilterCascader change
  const filterCascaderChange = (fieldName: string, value: (string | number)[][], selectedOptions: any, setLabel: any) => {
    filterInfoChange(fieldName, value);
    if (_.isEmpty(selectedOptions)) {
      setLabel('不限');
      return;
    }
    setLabel(handleDisplayText(selectedOptions));
  };

  // 反馈人下拉数据
  const feedbackPersonOptions = _.map(feedbackPerson?.dataList, item => {
    return {
      label: `${item?.userName}(${item?.userId})`,
      value: item?.userId,
    };
  });

  // 反馈人搜索
  const feedbackPersonSearch = (value: string) => {
    queryFeedbackPerson({ keyword: value, pageSize: 10, pageNum: 1 });
  };

  // 反馈人选择框change
  const feedbackPersonChange = (value: string) => {
    if (value) {
      filterInfoChange('empId', value);
    } else {
      filterInfoChange('empId', '');
      queryFeedbackPerson({ keyword: '', pageSize: 10, pageNum: 1 });
    }
    setFeedbackPersonValue(value ? value : '不限');
  };

  // 反馈人展开下拉菜单的回调
  const feedbackPersonVisibleChange = (visible: boolean) => {
    if (visible && feedbackPersonValue !== '不限' && _.size(feedbackPersonOptions) !== 1) {
      queryFeedbackPerson({ keyword: feedbackPersonValue, pageSize: 10, pageNum: 1 });
    }
  };

  const handleDepartmentChange = (valueObj: any) => {
    filterInfoChange('orgList', valueObj);
  };

  return (
    <div className={styles.filterInfo}>
      <Space size={32} className={styles.spaceSty} wrap={true}>
        <div className={styles.timeRange}>
          <span>时间范围：</span>
          <RangePicker
            defaultValue={defaultTimeRange || []}
            placeholder={['开始时间', '结束时间']}
            className={styles.datePicker}
            onChange={
              (_, dateStrings: [string, string]) => filterInfoChange('timeRange', dateStrings)
            }
          />
        </div>
        <div className={styles.feedbackPerson}>
          <Select
            isRemoteSearch={true}
            showSearch={true}
            bordered={false}
            allowClear={feedbackPersonValue !== '不限'}
            value={feedbackPersonValue}
            options={feedbackPersonOptions}
            defaultActiveFirstOption={false}
            loading={queryFeedbackPersonLoading}
            className={styles.feedbackPersonSelect}
            onSearch={feedbackPersonSearch}
            onChange={feedbackPersonChange}
            onDropdownVisibleChange={feedbackPersonVisibleChange}
          />
        </div>
        <div>
          <DepartmentTreeSelect
            defaultValue={defaultFeedbackDepartmentValue}
            treeData={department}
            onDepartmentChange={handleDepartmentChange}
            filterInfoChange={filterInfoChange}
            showCheckedStrategy={SHOW_ALL}
          />
        </div>
        {/* 数据类型为【点踩】(dislikes)时，显示反馈标签 */}
        {
          dataType === 'dislikes' && (
            <div>
              <FilterCascader
                multiple={true}
                defaultValue={defaultFeedbackTagsValue}
                options={dictData?.tagDict}
                fieldName="反馈标签："
                cascaderLabel={feedbackTagsLabel}
                onChange={
                  (value, selectOptions) =>
                    filterCascaderChange('feedbackTags', value, selectOptions, setFeedbackTagsLabel)
                }
                cascaderChildrenClassName={styles.cascaderDisplayRender}
                // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
                popupClassName={styles.cascaderDropdownClass}
              />
            </div>
          )
        }
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultFeedbackChannelValue}
            options={FeedbackChannelsEnum}
            fieldName="反馈渠道："
            cascaderLabel={feedbackChannelsLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('feedbackChannels', value, selectOptions, setFeedbackChannelsLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultSceneCoverageValue}
            options={SceneCoverageEnum}
            fieldName="场景覆盖："
            cascaderLabel={sceneCoverageLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('sceneCoverages', value, selectOptions, setSceneCoverageLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultAgentsValue}
            options={dictData?.agentDict}
            fieldName="涉及Agent："
            cascaderLabel={agentsLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('agents', value, selectOptions, setAgentsLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
        {
          dataType === 'dislikes' && (
            <div>
              <FilterCascader
                multiple={true}
                defaultValue={defaultAnalysisValue}
                options={IsAnalysisEnum}
                fieldName="是否分析："
                cascaderLabel={isAnalysisLabel}
                onChange={
                  (value, selectOptions) =>
                    filterCascaderChange('isAnalysis', value, selectOptions, setIsAnalysisLabel)
                }
                cascaderChildrenClassName={styles.cascaderDisplayRender}
                // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
                popupClassName={styles.cascaderDropdownClass}
              />
            </div>
          )
        }
        <div>
          <FilterCascader
            multiple={true}
            defaultValue={defaultEmpTypeValue}
            options={dictData?.tgTypeDict}
            fieldName="员工类型："
            cascaderLabel={empTypeLabel}
            onChange={
              (value, selectOptions) =>
                filterCascaderChange('tgType', value, selectOptions, setEmpTypeLabel)
            }
            cascaderChildrenClassName={styles.cascaderDisplayRender}
            // 此处使用 dropdownClassName 控制台会报错，所以继续使用 popupClassName
            popupClassName={styles.cascaderDropdownClass}
          />
        </div>
      </Space>
      <Button type="primary" onClick={handleQueryData}>查询</Button>
    </div>
  );
};

export default FilterInfo;
