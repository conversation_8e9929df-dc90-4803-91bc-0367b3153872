/**
 * @Author：wangjianglin<K1102084>
 * @Date：2025-05-15
 * @Description：数据反馈列表
 */
import React from 'react';
import _ from 'lodash';
import Button from '@/components/common/Button';
import { Space, Table, Tooltip } from '@ht/sprite-ui';
import type { ColumnsType } from '@ht/sprite-ui/lib/table';
import type {
  DataFeedbackList,
  DataFeedbackListItem,
} from '@/services/types/api/dataFeedback/queryDataFeedbackList';

import styles from './tableList.less';

type FileTableProps = {
  /** 数据类型 */
  dataType: string;
  dataFeedbackList: DataFeedbackList;
  loading: boolean;
  handleTraceId: (traceId: string) => void;
  handleDetail: (conversationId: string, empId: string) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
};
const TableList: React.FC<FileTableProps> = ({
  dataType,
  dataFeedbackList,
  loading,
  handleTraceId,
  handleDetail,
  handleTableChange,
}) => {

  // pagination对象
  const paginationProps = {
    showSizeChanger: true,
    pageSize: dataFeedbackList?.page?.pageSize || 20,
    current: dataFeedbackList?.page?.pageNum || 1,
    total: dataFeedbackList?.page?.totalCount || 0,
  };

  // 涉及Agent columns
  const agentsColumnsRender = (text: string, record: DataFeedbackListItem) => {
    const agentsName = _.map(record.agents, (item) => item.agentName).join('，');
    return (
      <Tooltip placement='topLeft' title={agentsName}>
        {agentsName}
      </Tooltip>
    );
  };

  // 反馈标签columns
  const feedbackTagsColumnsRender = (text: string, record: DataFeedbackListItem) => {
    const feedbackTagsName = _.map(record.feedbackTags, (item) => item.tagName).join('，');
    return (
      <Tooltip placement='topLeft' title={feedbackTagsName}>
        {feedbackTagsName}
      </Tooltip>
    );
  };

  // 详细原因columns
  const reasonsColumnsRender = (text: string, record: DataFeedbackListItem) => {
    return (
      <Tooltip placement='topLeft' title={text}>
        {text}
      </Tooltip>
    );
  };

  // traceIDcolumns
  const traceIdColumnsRender = (text: string, record: DataFeedbackListItem) => {
    return (
      <Tooltip placement='topLeft' title={text}>
        <a onClick={() => handleTraceId(record.traceId)}>{text}</a>
      </Tooltip>
    );
  };

  // 会话详情columns
  const actionColumnsRender = (text: string, record: DataFeedbackListItem) => {
    let empId = '';
    if (record.feedbackPerson) {
      empId = record?.feedbackPerson?.split(' ')?.[1] || record.feedbackPerson;
    }
    return (
      <Space size="middle" className={styles.actionRender}>
        <Button
          type="link"
          onClick={() => handleDetail(record.conversationId, empId)}
        >
          查看
        </Button>
      </Space>
    );
  };
  const columns: ColumnsType<DataFeedbackListItem> = [
    {
      title: '反馈人',
      dataIndex: 'feedbackPerson',
      width: 160,
    },
    {
      title: '所属部门/分支',
      width: 300,
      dataIndex: 'department',
    },
    {
      title: '员工类型',
      dataIndex: 'tgType',
      width: 160,
      render: (text) => {
        return text || '--';
      },
    },
    {
      title: '反馈时间',
      width: 160,
      dataIndex: 'feedbackTime',
      sorter: true,
      defaultSortOrder: 'descend',
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: '反馈渠道',
      width: 120,
      dataIndex: ['feedbackChannels', 'channelName'],
    },
    {
      title: '场景覆盖',
      width: 120,
      dataIndex: 'sceneCoverage',
    },
    {
      title: '涉及Agent',
      width: 140,
      dataIndex: 'agents',
      ellipsis: {
        showTitle: false,
      },
      render: agentsColumnsRender,
    },
    {
      title: '反馈标签',
      width: 170,
      dataIndex: 'feedbackTags',
      ellipsis: {
        showTitle: false,
      },
      render: feedbackTagsColumnsRender,
    },
    {
      title: '详细原因',
      width: 170,
      dataIndex: 'reasons',
      ellipsis: {
        showTitle: false,
      },
      render: reasonsColumnsRender,
    },
    {
      title: 'traceID',
      width: 200,
      dataIndex: 'traceId',
      ellipsis: {
        showTitle: false,
      },
      render: traceIdColumnsRender,
    },
    {
      title: '会话详情',
      key: 'action',
      width: 110,
      render: actionColumnsRender,
    },
    {
      title: '运营分析结果',
      width: 170,
      dataIndex: 'analysisResults',
      ellipsis: {
        showTitle: false,
      },
      render: reasonsColumnsRender,
    },
  ];

  // 根据 dataType 决定是否显示某些列
  const filteredColumns = columns.filter((column: any) => {
    // 【反馈标签】和【详细原因】在【点踩】(dislikes)时显示，否则不显示
    if (column.dataIndex === 'analysisResult' || column.dataIndex === 'feedbackTags'
      || column.dataIndex === 'reasons') {
      return dataType === 'dislikes';
    }
    // 其他列始终显示
    return true;
  });

  return (
    <div className={styles.tableList}>
      <Table
        rowKey="id"
        columns={filteredColumns}
        dataSource={dataFeedbackList.list}
        pagination={paginationProps}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default TableList;
