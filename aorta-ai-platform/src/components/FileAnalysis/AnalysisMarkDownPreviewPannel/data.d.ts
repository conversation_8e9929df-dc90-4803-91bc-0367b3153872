/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : MD阅览组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { IMarkdownResult } from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type { ISaveAnalysisMarkDown } from '@/services/types/api/fileManagement/saveAnalysisMarkDown';

export interface IProps {
  /** 当前文件解析后的MarkDown内容 */
  currentMarkDownResult: IMarkdownResult | null;
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 解析结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 原始文档预览是否收起 */
  originFilePreviewCollapsed: boolean;
  /** markdown预览是否收起 */
  markdownPreviewCollapsed: boolean;
  /** 点击收起/展开 */
  onCollapsedToggle: () => void;
   /** 保存MarkDown内容 */
  saveAnalysisMarkDown: (query: ISaveAnalysisMarkDown['Query']) => Promise<boolean>;
}
