/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 原始文档预览面板组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';

export interface IProps {
  /** 当前选则的文档 */
  currentFile: IFileInfo | null;
  /** 当前文档原始文档的预览地址 */
  currentOriginPreviewUrl: string | null;
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 原始文档预览是否收起 */
  originFilePreviewCollapsed: boolean;
  /** 文档分片预览是否收起 */
  chunkListCollapsed: boolean;
  /** 点击收起/展开 */
  onCollapsedToggle: () => void;
}
