/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档分片页面的源文档预览
 */
import type { IProps } from './data';

import React from 'react';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import CollapsedPannelTitle from '@/components/FileAnalysis/CollapsedPannelTitle';
import OriginFilePreview from '@/components/FileAnalysis/OriginFilePreview';

import useOriginStyle from './useOriginStyle';

import styles from './styles.less';

const ChunkOriginFilePreviewPannel = (props: IProps) => {
  const {
    currentFile,
    currentOriginPreviewUrl,
    originFilePreviewCollapsed,
    fileListCollapsed,
    chunkListCollapsed,
  } = props;

  // NOTE: SWB 2025-04-17 【文档列表】【源文档预览】【分片列表】三个都可各自收起
  // 因此当【文档列表】【分片列表】各自收起时，文档列表的
  const originPreviewStyle = useOriginStyle(fileListCollapsed, chunkListCollapsed);

  if (originFilePreviewCollapsed) {
    return (
      <div className={styles.collapsedOriginFilePreview}>
        <div className={styles.content}>
          <CollapsedPannelTitle
            title="原始文档预览"
            onCollapsedToggle={props.onCollapsedToggle}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.originFilePreview} style={originPreviewStyle}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="原始文档预览"
        />
        <OriginFilePreview
          fileInfo={currentFile}
          currentOriginPreviewUrl={currentOriginPreviewUrl}
        />
      </div>
    </div>
  );
};

export default ChunkOriginFilePreviewPannel;
