/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文件分片页面的MD阅览
 */
import type { IMarkDownEditorView } from '@/components/FileAnalysis/FileUrlMdEditor/data';
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import CollapsedPannelTitle from '@/components/FileAnalysis/CollapsedPannelTitle';
import AnalysisMarkdownFilePreview from '@/components/FileAnalysis/AnalysisMarkdownFilePreview';

import useMarkdownStyle from './useMarkdownStyle';

import styles from './styles.less';

const InitialMdView: IMarkDownEditorView = {
  uuid: uuid(),
  view: { menu: false, md: false, html: true },
};

const ChunksMarkDownPreviewPannel = (props: IProps) => {
  const {
    currentFile,
    currentMarkDownResult,
    fileAnalysisResult,
    fileListCollapsed,
    markdownPreviewCollapsed,
    chunkListCollapsed,
  } = props;

  // NOTE: SWB 2025-04-15 【文档列表】【MD阅览】【切片列表】三个都可各自收起
  // 因此当【文档列表】【切片列表】各自收起时，文档列表的
  const markdownPreviewStyle = useMarkdownStyle(fileListCollapsed, chunkListCollapsed);

  if (markdownPreviewCollapsed) {
    return (
      <div className={styles.collapsedMarkdownPreview}>
        <div className={styles.content}>
          <CollapsedPannelTitle
            title="MD阅览"
            onCollapsedToggle={props.onCollapsedToggle}
          />
        </div>
      </div>
    );
  }

  // NOTE: SWB 2025-04-16 找到当前文件的解析结果
  const currentAnalysisResult = _.find(fileAnalysisResult, item => item.fileId === currentFile?.fileId);

  return (
    <div className={styles.markdownPreview} style={markdownPreviewStyle}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="MD阅览"
        />
        <AnalysisMarkdownFilePreview
          mdEditorView={InitialMdView}
          currentAnalysisResult={currentAnalysisResult?.analysisResult}
          currentMarkDownResult={currentMarkDownResult}
        />
      </div>
    </div>
  );
};

export default ChunksMarkDownPreviewPannel;
