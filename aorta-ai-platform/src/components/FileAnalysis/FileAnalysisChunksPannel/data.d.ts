/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析面板页面的TS类型定义
 */
import type {
  IFileInfo,
  IGetFileInfoList,
} from '@/services/types/api/fileManagement/getFileInfoList';
import type {
  IGetAnalysisiMarkdownResult,
  IMarkdownResult,
} from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type {
  IGetMarkDownChunks,
  IFileChunkInfo,
} from '@/services/types/api/fileManagement/getMarkDownChunks';
import type {
  ICloudPreviewURL,
  IGetPreviewUrl,
} from '@/services/types/api/fileManagement/getPreviewUrl';
import type {
  IDeleteChunk,
} from '@/services/types/api/fileManagement/deleteChunk';
import type {
  ISaveChunk,
  ISaveChunkResult,
} from '@/services/types/api/fileManagement/saveChunk';
import type {
  ICheckoutChunk,
  ICheckoutChunkResult,
} from '@/services/types/api/fileManagement/checkoutChunk';
import type {
  IFileChunkProcessResult,
  IGetFileChunkProcess,
} from '@/services/types/api/fileManagement/getFileChunkProcess';
import type {
  IFileAnalysisResult,
} from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';
import type { PannelsName } from '@/components/FileAnalysis/config';

export interface IProps {
  /** 租户id */
  tenantId: string;
  /** 当前面板 */
  currentPannel: PannelsName[];
  /** 请求数据用的文件ID列表 */
  fileIdList: string[],
  /** 文档列表 */
  fileInfoList: IFileInfo[];
  /** 文档列表的解析进度 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 文档列表的切片进度 */
  fileChunkProcessResult: IFileChunkProcessResult[];
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: (query: IGetFileInfoList['Query']) => Promise<unknown>;
  /** 获取文件解析后的MarkDown文件内容 */
  getAnalysisMarkDown: (query: IGetAnalysisiMarkdownResult['Query']) => Promise<IMarkdownResult>;
  /** 获取文档切片列表 */
  getMarkDownChunks: (query: IGetMarkDownChunks['Query']) => Promise<IFileChunkInfo[]>;
  /** 获取源文档预览地址 */
  getPreviewUrl: (query: IGetPreviewUrl['Query']) => Promise<ICloudPreviewURL>;
  /** 删除文档分片 */
  deleteChunk: (query: IDeleteChunk['Query']) => Promise<boolean>;
  /** 保存文档切片 */
  saveChunk: (query: ISaveChunk['Query']) => Promise<ISaveChunkResult>;
  /** 一键校验文档切片 */
  checkoutChunk: (query: ICheckoutChunk['Query']) => Promise<ICheckoutChunkResult>;
  /** 上一步回调 */
  onPrevStep: () => void;
  /** 下一步回调 */
  onNextStep: () => void;
}

export interface IState {
  /** 当前文件的UUID，用于渲染React组件的Key */
  previewKey: string;
  /** 当前文档切片列表 */
  chunkList: ChunkInfoItem[];
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 当前文件解析后的MarkDown内容 */
  currentMarkDownResult: IMarkdownResult | null;
  /** 当前文档原始文档的预览地址 */
  currentOriginPreviewUrl: string | null;
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 文档切片列表是否收起 */
  chunkListCollapsed: boolean;
  /** 源文档或者markdown预览是否收起 */
  originFileOrmarkdownPreviewCollapsed: boolean;
}

export interface IChunkListPannelRefType {
  /** 当前【查看未校验切片】是否勾选 */
  checkoutFlagChecked: boolean;
  /** 取消【查看未校验切片】勾选 */
  cancleCheckoutFlag: () => void;
}
