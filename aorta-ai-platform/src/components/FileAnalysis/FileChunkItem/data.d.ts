/*
 * <AUTHOR> sun<PERSON>bin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档切片项的TS类型定义
 */
import type { IFileChunkInfo } from '@/services/types/api/fileManagement/getMarkDownChunks';
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';

export type ChunkInfoItem = IFileChunkInfo & {
  /** 编号 */
  no?: string;
  /** 是否编辑状态 */
  isEditing?: boolean,
  /** 是否临时增加的 */
  isTempAdd?: boolean,
}

export interface IProps {
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 总切片数 */
  totalCount: number;
  /** 当前切片信息 */
  chunkInfo: ChunkInfoItem;
  /** 删除文档分片 */
  onDeleteChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击进入分片编辑状态 */
  onEditChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击关闭分片编辑状态 */
  onCloseEdit: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片上面新增一个分片 */
  onUpAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片下面面新增一个分片 */
  onDownAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 保存当前文档分片 */
  onSaveChunk: (chunkInfo: ChunkInfoItem) => void;
}

export interface IState {
  /** 当前切片内容 */
  chunkContent: string;
  /** 当前切片摘要 */
  chunkSummary: string;
}
