/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 文档分片列表
 */
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Spin } from '@ht/sprite-ui';

import FileChunkItem from '@/components/FileAnalysis/FileChunkItem';

import styles from './styles.less';

const FileChunkList = (props: IProps) => {
  const {
    chunkList,
    currentFile,
    chunkTotalCount,
  } = props;

  if (_.isEmpty(chunkList)) {
    return (
      <div className={styles.noChunkList}>
        <Spin tip="文档分片中，请稍等" />
      </div>
    );
  }

  return (
    <div className={styles.fileChunkList}>
      {
        _.map(chunkList, (item) => {

          return (
            <FileChunkItem
              key={item.chunkId}
              currentFile={currentFile}
              chunkInfo={item}
              totalCount={chunkTotalCount}
              onDeleteChunk={props.onDeleteChunk}
              onEditChunk={props.onEditChunk}
              onCloseEdit={props.onCloseEdit}
              onDownAddChunk={props.onDownAddChunk}
              onUpAddChunk={props.onUpAddChunk}
              onSaveChunk={props.onSaveChunk}
            />
          );
        })
      }
    </div>
  );
};
export default React.memo(FileChunkList);
