/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-09-03
 * @Description  : 判断切片校验状态
 */
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';

import { useEffect, useState } from 'react';
import _ from 'lodash';

interface CheckoutStatus {
  /** 已校验切片数量文案 */
  checkoutCountText: string;
  /** 切片是否全部已校验 */
  allCheckedouted: boolean;
  /** 切片总数 */
  chunkTotalCount: number;
  /** 操作是否禁用 */
  operateDisabled: boolean;
}

const DEFAULT_CHECKOUT_STATUS:CheckoutStatus = {
  checkoutCountText: '',
  allCheckedouted: false,
  operateDisabled: false,
  chunkTotalCount: 0,
};

/**
 * 获取切片列表中整体的已校验切片状态
 * @param chunkList 切片列表
 * @returns {CheckoutStatus}
 */
function useCheckoutStatus(chunkList: ChunkInfoItem[]): CheckoutStatus {
  const [checkoutStatus, setCheckoutStatus] = useState(DEFAULT_CHECKOUT_STATUS);

  useEffect(() => {
    // 1. 切片总数
    const chunkTotalCount = _.size(chunkList);
    // 2. 因为切片列表中的最后的文章总结(【conclusion】字段为true)不算在内，所以此处数量什么的需要将其排除掉计算
    const filteredList = _.filter(chunkList, (item: ChunkInfoItem) => !item.conclusion);
    const filteredChunkTotalCount = _.size(filteredList);
    // 计算出已校验的切片数量
    const checkoutChunkCount = _.reduce(filteredList, (acc, item) => {
      if (item.checkoutFlag) {
        // eslint-disable-next-line no-param-reassign
        acc += 1;
      }
      return acc;
    }, 0);

    // 3. 判断如果切片列表中存在编辑状态的切片，则将校验相关操作disabled
    const editingChunk = _.find(chunkList, (item: ChunkInfoItem) => item.isEditing) as ChunkInfoItem | null;
    const operateDisabled = !_.isEmpty(editingChunk);

    setCheckoutStatus({
      checkoutCountText: `已校验${checkoutChunkCount}/${filteredChunkTotalCount}`,
      allCheckedouted: filteredChunkTotalCount === checkoutChunkCount,
      chunkTotalCount,
      operateDisabled,
    });
  }, [chunkList]);

  return checkoutStatus;
}

export default useCheckoutStatus;
