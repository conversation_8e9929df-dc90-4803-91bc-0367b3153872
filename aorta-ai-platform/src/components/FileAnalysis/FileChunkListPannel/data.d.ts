/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : MD阅览组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { IMarkdownResult } from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';

import type React from 'react';

export interface IProps {
  ref: React.RefObject<unknown>;
  /** 当前文档切片列表 */
  chunkList: ChunkInfoItem[];
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 文档列表是否收起 */
  fileListCollapsed: boolean;
  /** 文档切片是否收起 */
  chunkListCollapsed: boolean;
  /** 源文档或者MarkDown阅览是否收起 */
  originFileOrmarkdownPreviewCollapsed: boolean;
  /** 点击收起/展开 */
  onCollapsedToggle: () => void;
  /** 删除文档分片 */
  onDeleteChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击进入分片编辑状态 */
  onEditChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 点击关闭分片编辑状态 */
  onCloseEdit: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片上面新增一个分片 */
  onUpAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 在当前分片下面面新增一个分片 */
  onDownAddChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 保存当前文档分片 */
  onSaveChunk: (chunkInfo: ChunkInfoItem) => void;
  /** 一键校验文档切片 */
  onCheckoutChunk: () => void;
  /** 检查切片是否可以用于一键校验 */
  onValidateChunks: () => boolean;
}
