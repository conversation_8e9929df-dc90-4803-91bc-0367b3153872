/*
 * <AUTHOR> sunweibin<********>
 * @Date         : 2025-04-17
 * @Description  : 文档分片列表
 */
import type { IProps } from './data';

import React, { useState, useImperativeHandle } from 'react';
import _ from 'lodash';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import CollapsedPannelTitle from '@/components/FileAnalysis/CollapsedPannelTitle';
import FileChunkList from '@/components/FileAnalysis/FileChunkList';
import FileChunkListCheckoutArea from '@/components/FileAnalysis/FileChunkListCheckoutArea';

import useMarkdownStyle from './useMarkdownStyle';
import useFilteredChunkList from './useFilteredChunkList';

import styles from './styles.less';

const FileChunkListPannel = React.forwardRef((props: IProps, ref) => {
  const {
    currentFile,
    chunkList,
    fileListCollapsed,
    originFileOrmarkdownPreviewCollapsed,
    chunkListCollapsed,
  } = props;

  const [checkoutFlagChecked, setCheckoutFlagChecked] = useState(false);
  const { filteredChunkList, chunkTotalCount } = useFilteredChunkList(chunkList, checkoutFlagChecked);

  useImperativeHandle(ref, () => ({
    checkoutFlagChecked,
    // 给外部能够取消选中
    cancleCheckoutFlag: () => {
      setCheckoutFlagChecked(false);
    },
  }), [checkoutFlagChecked]);

  const handleCheckoutFlagToggle = (cheked: boolean) => {
    setCheckoutFlagChecked(cheked);
  };

  // NOTE: SWB 2025-04-17 【文档列表】（【MD阅览】/【源文档阅览】）【切片列表】三个都可各自收起
  // 因此当【文档列表】（【MD阅览】/【源文档阅览】）各自收起时，文档列表的
  const markdownPreviewStyle = useMarkdownStyle(fileListCollapsed, originFileOrmarkdownPreviewCollapsed);

  if (chunkListCollapsed) {
    return (
      <div className={styles.collapsedChunkListPreview}>
        <div className={styles.content}>
          <CollapsedPannelTitle
            title="分片预览"
            onCollapsedToggle={props.onCollapsedToggle}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.chunkListPreview} style={markdownPreviewStyle}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="分片预览"
          rightContent={(
            <FileChunkListCheckoutArea
              chunkList={chunkList}
              checkoutFlagChecked={checkoutFlagChecked}
              onCheckoutFlagToggle={handleCheckoutFlagToggle}
              onCheckoutChunk={props.onCheckoutChunk}
              onValidateChunks={props.onValidateChunks}
            />
          )}
        />
        <FileChunkList
          currentFile={currentFile}
          chunkTotalCount={chunkTotalCount}
          chunkList={filteredChunkList}
          onDeleteChunk={props.onDeleteChunk}
          onEditChunk={props.onEditChunk}
          onCloseEdit={props.onCloseEdit}
          onDownAddChunk={props.onDownAddChunk}
          onUpAddChunk={props.onUpAddChunk}
          onSaveChunk={props.onSaveChunk}
        />
      </div>
    </div>
  );
});

export default React.memo(FileChunkListPannel);
