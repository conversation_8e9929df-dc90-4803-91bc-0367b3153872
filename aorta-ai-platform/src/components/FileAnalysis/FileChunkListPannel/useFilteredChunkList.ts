/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-09-03
 * @Description  : 根据其他项的展开和收起宽度变化
 */
import type { ChunkInfoItem } from '@/components/FileAnalysis/FileChunkItem/data';

import { useEffect, useState } from 'react';
import _ from 'lodash';

interface FilteredChunkList {
  /** 过滤后的切片列表 */
  filteredChunkList: ChunkInfoItem[];
  /** 文档所有切片总数 */
  chunkTotalCount: number;
}

/**
 * 获取过滤后的切片列表
 * @param chunkList 文档所有的切片列表
 * @param checkoutFlagChecked 是否查看未校验切片
 * @returns
 */
function useFilteredChunkList(chunkList: ChunkInfoItem[], checkoutFlagChecked: boolean): FilteredChunkList {
  const [filteredChunkList, setFilteredChunkList] = useState<ChunkInfoItem[]>(chunkList);
  const [chunkTotalCount, setChunkTotalCount] = useState(0);

  useEffect(() => {
    // 1. 用于页面渲染切片列表
    //  1.1 如果【checkoutFlagChecked】为false，则展示整个切片列表
    //  1.2 如果【checkoutFlagChecked】为true时
    //    1.2.1 【文章总结】不能展示,并展示所有未校验的切片
    //    1.2.2 当所有切片一个个全部编辑并校验完成，则展示整个切片列表
    let list = chunkList;

    // 排除【文章总结】后的切片列表
    const excludConclusionList = _.filter(chunkList, (item: ChunkInfoItem) => !item.conclusion);
    const filteredList = _.filter(excludConclusionList, (item: ChunkInfoItem) => !item.checkoutFlag);

    if (checkoutFlagChecked) {
      list = filteredList;
    }

    setFilteredChunkList(list);

    setChunkTotalCount(_.size(excludConclusionList));

  }, [chunkList, checkoutFlagChecked]);

  return {
    filteredChunkList,
    chunkTotalCount,
  };
}

export default useFilteredChunkList;
