/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 文档列表项组件的TS定义
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IFileAnalysisResult } from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type { IFileChunkProcessResult } from '@/services/types/api/fileManagement/getFileChunkProcess';

/** 文档可以用于选中 */
interface ISelectableProps {
  /** 是否可以被选中 */
  selectable: true;
  /** 是否选中 */
  selected: boolean;
  /** 选择文件 */
  onSelect: (fileInfo: IFileInfo) => void;
}

/** 文档不可以可以用于选中 */
interface INotSelectableProps {
  /** 是否可以被选中 */
  selectable: false;
}

/** 文档列表项的公共Props */
interface ICommonProps {
  /** 是否折叠状态 */
  collapsed: boolean;
  /** 文档信息列表 */
  fileInfo: IFileInfo;
  /** 文件解析进度结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 文件分片进度结果 */
  fileChunkProcessResult: IFileChunkProcessResult[];
}


export type IProps = ICommonProps & (ISelectableProps | INotSelectableProps);
