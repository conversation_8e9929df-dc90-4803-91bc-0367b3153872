/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件列表
 */
import type { IFileInfo } from '@/services/types/api/fileManagement/getFileInfoList';
import type { IProps } from './data.d';

import React, { useCallback } from 'react';
import _ from 'lodash';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import FileListCollapsedTitle from '@/components/FileAnalysis/FileListCollapsedTitle';
import FileInfoItem from '@/components/FileAnalysis/FileInfoItem';

import styles from './styles.less';

const FileList = (props: IProps) => {
  const {
    fileListCollapsed,
    fileInfoList,
    currentFile,
  } = props;

  const renderFileItem = useCallback(
    (item: IFileInfo) => {
      const { fileId } = item;

      const isSelectd = fileId === currentFile?.fileId;

      return (
        <FileInfoItem
          key={fileId}
          selectable={true}
          collapsed={fileListCollapsed}
          selected={isSelectd}
          fileInfo={item}
          onSelect={props.onSelectFile}
        />
      );
    },
    [currentFile, fileListCollapsed, props.onSelectFile],
  );

  if (fileListCollapsed) {
    return (
      <div className={styles.collapsedFileList}>
        <div className={styles.content}>
          <FileListCollapsedTitle onCollapsedToggle={props.onCollapsedToggle} />
          <div className={styles.fileListArea}>
            {_.map(fileInfoList, renderFileItem)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.fileList}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="文档列表"
        />
        <div className={styles.fileListArea}>
          {_.map(fileInfoList, renderFileItem)}
        </div>
      </div>
    </div>
  );
};

export default FileList;
