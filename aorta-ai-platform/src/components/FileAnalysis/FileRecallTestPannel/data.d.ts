/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 文件解析面板页面的TS类型定义
 */
import type {
  IFileInfo,
  IGetFileInfoList,
} from '@/services/types/api/fileManagement/getFileInfoList';
import type {
  IFileAnalysisResult,
} from '@/services/types/api/fileManagement/getFileAnalysisResult';
import type {
  IGetAnalysisiMarkdownResult,
  IMarkdownResult,
} from '@/services/types/api/fileManagement/getAnalysisMarkDown';
import type {
  ICloudPreviewURL,
  IGetPreviewUrl,
} from '@/services/types/api/fileManagement/getPreviewUrl';
import type {
  IRecallTest,
  IRecallTestResult,
} from '@/services/types/api/fileManagement/recallTest';
import type { PannelsName } from '@/components/FileAnalysis/config';

export interface IProps {
  /** 当前面板 */
  currentPannel: PannelsName[];
  /** 请求数据用的文件ID列表 */
  fileIdList: string[],
  /** 文档列表 */
  fileInfoList: IFileInfo[];
  /** 文档列表的解析进度结果 */
  fileAnalysisResult: IFileAnalysisResult[];
  /** 获取文档解析等页面需要的文档信息 */
  getFileInfoList: (query: IGetFileInfoList['Query']) => Promise<unknown>;
  /** 召回测试 */
  recallTest: (query: IRecallTest['Query']) => Promise<IRecallTestResult[]>;
  /** 上一步回调 */
  onPrevStep: () => void;
  /** 完成回调 */
  onComplete: () => void;
}

export interface IState {
  /** 是否展示【上一步】按钮 */
  showPrevStepBtn: boolean;
  /** 当前文件 */
  currentFile: IFileInfo | null;
  /** 相似度阈值 */
  thresholdValue: number | null;
  /** 输入文案 */
  keyword: string;
  /** 召回结果 */
  recallResult: IRecallTestResult[];
  /** 用于Modal展示的召回结果 */
  selectedRecallResult: IRecallTestResult | null;
  /** 打开Modal */
  openModal: boolean;
}
