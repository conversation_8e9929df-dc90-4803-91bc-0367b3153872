.fileRecallTestPannel {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 24px;
  width: 100%;
  height: 100%;
  border-radius: 24px 24px 0 0;
  z-index: 5;
  background-color: #fff;
  overflow: hidden;
  box-sizing: border-box;

  .processArea {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88px;
    padding: 0 136px 0 152px;
    box-sizing: border-box;
  }

  .contentWrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: calc(100% - 72px - 88px - 72px);
    overflow: hidden;

    .leftArea {
      flex: 0 0 auto;
      margin-right: 16px;
      width: 264px;
      height: 100%;
      box-sizing: border-box;
    }

    .rightArea {
      flex: 0 0 auto;
      width: calc(100% - 264px - 16px);
      height: 100%;
      border-radius: 2px;
      border: 1px solid #dddee0;
      box-sizing: border-box;
      overflow: auto;

      .inputHeader {
        position: sticky;
        top: 0;
        z-index: 6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 54px;
        padding: 15px 16px;
        box-sizing: border-box;
        background-color: #fff;
        box-shadow: inset 0 -1px 0 0 #f2f2f3;

        .headerTitle {
          flex: 0 0 auto;
          height: 24px;
          font-size: 16px;
          color: #3f434b;
          line-height: 24px;
          font-weight: bold;
        }

        .inputBtnArea {
          flex: 0 0 auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 0 12px;
          height: 72px;
        }
      }

      .keywordArea {
        padding: 8px 16px 32px;
        width: 100%;
        box-sizing: border-box;

        .keywordTextArea {
          width: 100%;
          height: 96px;
          resize: none;
        }
      }

      .resultArea {
        padding: 0 16px;
        width: 100%;
        box-sizing: border-box;

        .resultTitle {
          width: 100%;
          height: 24px;
          margin-bottom: 8px;
          font-size: 16px;
          font-weight: bold;
          color: #3f434b;
          line-height: 24px;
        }

        .resultList {
          width: 100%;
          box-sizing: border-box;

          .noResult {
            width: 100%;
            height: calc(100vh - 382px - 64px - 72px - 2px);
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .resultData {
            margin-bottom: 16px;
            display: grid;
            grid-template-columns: repeat(3, minmax(0, 1fr));
            grid-template-rows: repeat(auto-fill, 178px);
            gap: 16px;
            width: 100%;
            box-sizing: border-box;
            overflow: auto;
          }
        }
      }
    }
  }

  .btnArea {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0 12px;
    width: 100%;
    height: 72px;
  }
}
