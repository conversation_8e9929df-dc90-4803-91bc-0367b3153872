import React, { useEffect, useState } from 'react';
import { Steps, Button } from '@ht/sprite-ui';
import isEmpty from 'lodash/isEmpty';
import FileUploader from '@/components/FileAnalysis/FileUploader';
import type { FileUploadPanelProps } from './interface';
import styles from './index.less';

const FileUploadPanel: React.FC<FileUploadPanelProps> = props => {
  const { onNextStep, categoryId } = props;
  const [disableNextStep, setDisableNextStep] = useState<boolean>(true);
  const [fileList, setFileList] = useState<any[]>([]);
  const [isQueueFinished, setQueueFinished] = useState(false);

  const handleNextStep = () => {
    const newFileIdList: string[] = [];
    fileList?.forEach((item: any) => {
      if(item.status === 'done') {
        newFileIdList.push(item.attachId);
      }
    });
    onNextStep(newFileIdList);
  };

  useEffect(() => {
    if(!isQueueFinished && !isEmpty(fileList)){
      const newFileList: string[] = [];
      fileList?.forEach((item: any) => {
        if(item.status === 'done') {
          newFileList.push(item.attachId);
        }
      });
      // 仅过滤上传成功的文件至下一步
      if(!isEmpty(newFileList)){
        setDisableNextStep(false);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(fileList), isQueueFinished]);

  return (
    <div className={styles.fileUploaderPanel}>
      <div className={styles.processArea}>
        <Steps current={0}>
          <Steps.Step title="文件上传及接入" />
          {/* <Steps.Step title="文件解析" /> */}
          <Steps.Step title="文件切片" />
          {/* <Steps.Step title="数据处理" /> */}
          <Steps.Step title="命中测试" />
        </Steps>
      </div>
      <FileUploader
        categoryId={categoryId}
        fileList={fileList}
        setFileList={setFileList}
        setQueueFinished={setQueueFinished}
      />
      <div className={styles.btnArea}>
        <Button
          type="primary"
          disabled={disableNextStep}
          onClick={handleNextStep}
        >
          下一步
        </Button>
      </div>
    </div>
  );
};

export default FileUploadPanel;
