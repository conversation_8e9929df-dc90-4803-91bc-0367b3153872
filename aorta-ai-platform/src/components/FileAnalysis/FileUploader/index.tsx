import type { RcFile } from '@ht/sprite-ui/lib/upload';
import type { FileUploaderProps } from './interface';

import React, { useRef, useEffect, useState } from 'react';
import { Modal, Upload, Button, Table, Progress, message, Space } from '@ht/sprite-ui';
import { InboxOutlined, InfoCircleFilled } from '@ht-icons/sprite-ui-react';
import { connect } from '@oula/oula';
import isEmpty  from 'lodash/isEmpty';
import classNames from 'classnames';

import { formatFileSize, getFileFormat } from './utils';
import useTableScroll from './useTableScroll';

import styles from './index.less';

// 配置常量
const MAX_FILE_COUNT = 100;  // 最大文件数
const CONCURRENT_UPLOADS = 5;  // 并发数
const ALLOWED_TYPES = '.md,.doc,.docx,.xls,.xlsx,.pdf,.html';

const FileUploader: React.FC<FileUploaderProps> = (props) => {
  const { dispatch, tenantId, categoryId, fileList, setFileList, setQueueFinished } = props;
  const [uploading, setUploading] = useState(false);
  const uploadQueue = useRef([]);
  const activeUploads = useRef(new Set());

  const tableScroll = useTableScroll();

  useEffect(() => {
    if(uploadQueue.current.length === 0 && !uploading){
      setQueueFinished(true);
    } else {
      setQueueFinished(false);
    }
  }, [setQueueFinished, uploading, uploadQueue.current.length]);

  // 自定义上传实现
  const onUploadFile = async (file: RcFile) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('tenantId', tenantId);
    formData.append('categoryId', categoryId);

    try {
      const resultData =  await dispatch?.({
        type: 'fileManagement/uploadFile',
        payload: {
          formData,
          onUploadProgress: (e: ProgressEvent) => {
            const percent = Math.round(e.loaded * 100 / e.total);
            console.log('进度', percent);
            updateFileProgress(file.uid, percent);
          },
        },
      });
      if (!isEmpty(resultData.attachId)) {
        updateFileStatus(file.uid, 'done', resultData.attachId);
      } else {
        throw new Error('上传失败');
      }
    } catch (error) {
      console.error('上传错误:', error);
      updateFileStatus(file.uid, 'error');
    } finally {
      // 上传完成后从活跃上传列表中移除
      activeUploads.current.delete(file.uid);
      processUploadQueue();
    }
  };

  // 更新文件进度
  const updateFileProgress = (uid: string, percent: number) => {
    // @ts-expect-error
    setFileList((prev: RcFile[]) => prev?.map((item: RcFile) =>
      item.uid === uid ? { ...item, percent } : item,
    ));
  };

  // 更新文件状态
  const updateFileStatus = (uid: string, status: string, attachId?: string) => {
    // @ts-expect-error
    setFileList(prev => prev?.map((item: any) =>
      item.uid === uid ? { ...item, status, attachId } : item,
    ));
  };

  // 处理上传队列
  const processUploadQueue = () => {
    // 检查是否有空闲的上传槽位
    const availableSlots = CONCURRENT_UPLOADS - activeUploads.current.size;

    if (availableSlots > 0 && uploadQueue.current.length > 0) {
      // 从队列中取出文件开始上传
      const filesToUpload = uploadQueue.current.splice(0, availableSlots);

      filesToUpload.forEach((file: RcFile) => {
        activeUploads.current.add(file.uid);
        updateFileStatus(file.uid, 'uploading');
        onUploadFile(file);
      });
    }

    // 更新全局上传状态
    setUploading(activeUploads.current.size > 0);
  };

  // 添加到上传队列
  const addToUploadQueue = (file: RcFile) => {
    // @ts-expect-error
    uploadQueue.current.push(file);
    // @ts-expect-error
    setFileList(prev => [...prev, {
      uid: file.uid,
      name: file.name,
      size: file.size,
      status: 'waiting',
      percent: 0,
      originFileObj: file,
    }]);
  };

  // 上传预处理
  const beforeUpload = (file: RcFile, files: RcFile[]) => {
    // 检查文件总数
    if (fileList.length + files.length > MAX_FILE_COUNT) {
      message.error(`最多只能上传${MAX_FILE_COUNT}个文件`);
      return false;
    }

    // 检查文件大小 (示例: 限制100MB)
    const MAX_SIZE = 100 * 1024 * 1024; // 100MB
    if (file.size > MAX_SIZE) {
      message.error(`${file.name} 超过大小限制 (100MB)`);
      return false;
    }

    // 添加到上传队列
    addToUploadQueue(file);

    // 返回 false 以阻止默认上传行为
    return false;
  };

  // 自动处理上传队列
  useEffect(() => {
    processUploadQueue();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(fileList.length)]);


  // 二次确认删除
  const confirmDelete = (file: any) => {
    Modal.confirm({
      title: '确定要删除吗？',
      icon: <InfoCircleFilled style={{ color: '#FAAD14' }}/>,
      content: <></>,
      okText: '确定',
      cancelText: '取消',
      maskClosable: true,
      onOk() {
        handleRemove(file);
      },
      onCancel() {},
    });
  };

  // 移除文件
  const handleRemove = async (file: any) => {
    try {
      if(file.attachId && dispatch) {
        const resultData = await dispatch({
          type: 'fileManagement/deleteFile',
          payload: {
            fileId: file.attachId,
          },
        });
        // 删除成功
        if (resultData) {
          // 从队列中移除
          uploadQueue.current = uploadQueue.current.filter((f: any) => f.uid !== file.uid);

          // 如果正在上传，需要取消请求 (实际项目中可以实现)
          if (activeUploads.current.has(file.uid)) {
            // 这里可以添加取消上传的逻辑
            activeUploads.current.delete(file.uid);
          }

          // 从显示列表中移除
          // @ts-expect-error
          setFileList(prev => prev.filter((f:any) => f.uid !== file.uid));
        } else {
          throw new Error('删除失败');
        }
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 重试上传
  const retryUpload = (file: any) => {
    const originFile = file.originFileObj;
    if (originFile) {
      handleRemove(file);
      addToUploadQueue(originFile);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '文件名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: any) => <span style={{ fontWeight: 500 }}>{text}</span>,
    },
    {
      title: '文件格式',
      dataIndex: 'name',
      key: 'format',
      render: (text: string) => getFileFormat(text),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => {
        if (status === 'uploading') {
          return (
            <Space>
              <Progress
                type="circle"
                percent={record.percent || 0}
                width={24}
                strokeColor="#1890ff"
                format={() => ''}
              />
              <span>{record.percent || 0}%</span>
            </Space>
          );
        } else{
          const statusCls = classNames({
            [styles.success]: status === 'done',
            [styles.failed]: status === 'error',
          });
          return (
            <div className={styles.statusWrap}>
              <div className={statusCls} >{status === 'done' ? '上传成功' : '上传失败'}</div>
            </div>
          );
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button
            type="text"
            onClick={() => confirmDelete(record)}
            disabled={record.status === 'uploading'}
          >
            删除
          </Button>
          {record.status === 'error' && (
            <Button
              type="link"
              size="small"
              onClick={() => retryUpload(record)}
            >
              重试
            </Button>
          )}
        </>
      ),
    },
  ];

  return (
    <div>
      <div className={styles.uploaderWrapper}>
        <Upload.Dragger
          multiple={true}
          showUploadList={false}
          beforeUpload={beforeUpload}
          accept={ALLOWED_TYPES} //  限制选择文件类型
          className={styles.uploadDragger}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ color: '#1890ff', fontSize: '48px' }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
          <p className="ant-upload-hint">
            支持PDF、DOC、DOCX、MD、Excel，每次最多上传100个文件，每个文件不超过 20MB
            {uploading && ' (自动上传中...)'}
          </p>
        </Upload.Dragger>
      </div>
      <div className={styles.statusBar}>
        <InfoCircleFilled className={styles.infoIcon} />
        <span  className={styles.statusBarText}>
          注意: 共计上传{fileList.length}篇文档，已成功{fileList?.filter((i:any) => i.status === 'done')?.length}篇, 失败{fileList?.filter((i:any) => i.status === 'error')?.length}篇，等待上传{fileList?.filter((i:any) => i.status === 'waiting')?.length}篇，请勿关闭页面
        </span>
      </div>
      <Table
        columns={columns}
        dataSource={fileList}
        rowKey="uid"
        pagination={false}
        scroll={tableScroll}
        size="small"
        bordered={true}
      />
    </div>
  );
};

export default connect(({ global }: any) => ({
  tenantId: global.tenantId,
}))(FileUploader);
