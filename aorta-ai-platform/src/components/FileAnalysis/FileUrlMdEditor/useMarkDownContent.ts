/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-17
 * @Description  : 通过MD文件流获取MarkDown文件的文字内容
 */

import type React from 'react';
import { useEffect, useState } from 'react';

type ContentStateType = [string, React.Dispatch<React.SetStateAction<string>>];

/**
 * 获取【MD阅览】的文件的文字内容
 * @param fileUrl MarkDown文件地址
 * @returns MD文件的文字内容
 */
function useMarkDownContent(fileUrl: string, initialFn?: (text: string) => void): ContentStateType {
  const [content, setContent] = useState('');

  useEffect(() => {
    setContent('');

    fetch(fileUrl)
      .then(response => response.text())
      .then((text) => {
        setContent(text);
        initialFn?.(text);
      }).catch(error => console.error('===swb===useMarkDownContent::Error:', error));
  }, [fileUrl, initialFn]);

  return [content, setContent];
}

export default useMarkDownContent;
