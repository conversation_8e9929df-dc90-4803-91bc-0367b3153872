/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-15
 * @Description  : 原始文档预览
 */
import type { IProps } from './data';

import React from 'react';
import _ from 'lodash';
import { Empty } from '@ht/sprite-ui';

import EmptyPng from '@/assets/empty.png';
import IFrameContent from '@/components/common/IFrameContent';
import { FileTypeEnum } from '@/components/FileAnalysis/config';

import { getHtmlFileUrl } from './utils';

import styles from './styles.less';

const OriginFilePreview = (props: IProps) => {
  const { fileInfo, currentOriginPreviewUrl } = props;

  // 1. 判断是否网址
  const isHTML = fileInfo?.fileType === FileTypeEnum.HTML;
  // 2. 非html文件需要判断是否存在云文档地址
  const existCloudUrl = !_.isEmpty(currentOriginPreviewUrl);

  if (isHTML) {
    // 3. html文件需要前端自行拼接文件地址
    const fileUrl = getHtmlFileUrl(fileInfo?.fileId, fileInfo?.fileName);

    return (
      <div className={styles.iframeContent}>
        <IFrameContent
          fileType={fileInfo?.fileType}
          title={fileInfo?.fileName}
          fileUrl={fileUrl}
        />
      </div>
    );
  }

  if (!isHTML && existCloudUrl) {
    return (
      <div className={styles.iframeContent}>
        <iframe
          className={styles.iframe}
          title={fileInfo?.fileName}
          src={currentOriginPreviewUrl as string}
        />
      </div>
    );
  }

  return (
    <div className={styles.noPreviewContent}>
      <Empty description="该格式暂无法支持预览" image={EmptyPng} />
    </div>
  );
};

export default OriginFilePreview;
