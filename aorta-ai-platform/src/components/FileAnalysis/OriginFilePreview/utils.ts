/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-05-28
 * @Description  : 当前组件的辅助函数
 */

/**
 * 拼接html文件的S3文件流下载地址
 * @param {string} attachId  文件ID
 * @param {string} filename 文件名称
 * @returns {string} html文件的S3文件流下载地址
 */
export function getHtmlFileUrl(attachId: string, filename: string): string {
  const path = '/fspa/aorta/ai/api/desktop/s3file/download';
  return `${path}?attachId=${attachId}&filename=${filename}`;
}
