/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 原始文档预览
 */
import type { IProps } from './data';

import React from 'react';

import PannelTitle from '@/components/FileAnalysis/PannelTitle';
import CollapsedPannelTitle from '@/components/FileAnalysis/CollapsedPannelTitle';
import OriginFilePreview from '@/components/FileAnalysis/OriginFilePreview';

import useOriginStyle from './useOriginStyle';

import styles from './styles.less';

const OriginFilePreviewPannel = (props: IProps) => {
  const {
    currentFile,
    currentOriginPreviewUrl,
    originFilePreviewCollapsed,
    fileListCollapsed,
    markdownPreviewCollapsed,
  } = props;

  // NOTE: SWB 2025-04-15 【文档列表】【源文档预览】【MD预览】三个都可各自收起
  // 因此当【文档列表】【源文档预览】各自收起时，文档列表的
  const originPreviewStyle = useOriginStyle(fileListCollapsed, markdownPreviewCollapsed);

  if (originFilePreviewCollapsed) {
    return (
      <div className={styles.collapsedOriginFilePreview}>
        <div className={styles.content}>
          <CollapsedPannelTitle
            title="原始文档预览"
            onCollapsedToggle={props.onCollapsedToggle}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.originFilePreview} style={originPreviewStyle}>
      <div className={styles.content}>
        <PannelTitle
          onCollapsedToggle={props.onCollapsedToggle}
          title="原始文档预览"
        />
        <OriginFilePreview
          fileInfo={currentFile}
          currentOriginPreviewUrl={currentOriginPreviewUrl}
        />
      </div>
    </div>
  );
};

export default OriginFilePreviewPannel;
