/*
 * <AUTHOR> sunweibin<K0100008>
 * @Date         : 2025-04-14
 * @Description  : 非折叠状态下的标题
 */
import type { IProps } from './data.d';

import React from 'react';
import { OutdentOutlined } from '@ht-icons/sprite-ui-react';

import styles from './styles.less';

const PannelTitle = (props: IProps) => {
  const { title, rightContent = null } = props;

  return (
    <div className={styles.pannelTitle}>
      <div className={styles.icon} onClick={props.onCollapsedToggle}>
        <OutdentOutlined />
      </div>
      <div className={styles.text}>{title}</div>
      <div className={styles.extra}>{rightContent}</div>
    </div>
  );
};

export default PannelTitle;
