/* eslint-disable max-len */
/*
 * <AUTHOR> wangjianglin
 * @Date         : 2025-01-24
 * @Description  : 核查信息-产品交易佣金参数-标准佣金参数
 */
import React from 'react';
import { Row, Col } from 'antd';
import PropTypes from 'prop-types';

import Tooltip from '@/components/common/Tooltip';
import IFWrap from '@/components/common/IFWrap';
import BlockHeader from '../Common/BlockHeader';
import {
  NoSetText,
  YesOrNo,
  YOrN,
} from '../config';

import styles from './standardCommissionContent.less';

function StandardCommissionContent(props) {
  const { data } = props;

  return (
    <div className={styles.standardCommissionContent}>
      <div className={styles.contentInfo}>
        <BlockHeader title="普通账户" />
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>股基：</span>
            <span className={styles.itemValue}>{data?.basicCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>债券：</span>
            <span className={styles.itemValue}>{data?.zqCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>场内基金：</span>
            <span className={styles.itemValue}>{data?.oCommission?.label || NoSetText}</span>
          </Col>
        </Row>
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>大宗交易：</span>
            <span className={styles.itemValue}>{data?.dCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>权证：</span>
            <span className={styles.itemValue}>{data?.qCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>回购：</span>
            <span className={styles.itemValue}>{data?.hCommission?.label || NoSetText}</span>
          </Col>
        </Row>
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>港股通（净佣金）：</span>
            <span className={styles.itemValue}>{data?.hkCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>股转：</span>
            <Tooltip title={data?.stbCommission?.label || NoSetText} placement="top">
              <span className={styles.itemValue}>{data?.stbCommission?.label || NoSetText}</span>
            </Tooltip>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>B股：</span>
            <span className={styles.itemValue}>{data?.bgCommission?.label || NoSetText}</span>
          </Col>
        </Row>
      </div>
      <div className={styles.contentInfo}>
        <BlockHeader title="信用账户" />
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>担保股基：</span>
            <span className={styles.itemValue}>{data?.stkCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>信用股基：</span>
            <span className={styles.itemValue}>{data?.creditCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>担保债券：</span>
            <span className={styles.itemValue}>{data?.dzCommission?.label || NoSetText}</span>
          </Col>
        </Row>
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>信用债券：</span>
            <span className={styles.itemValue}>{data?.creditBondsCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>担保场内基金：</span>
            <span className={styles.itemValue}>{data?.doCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>信用场内基金：</span>
            <span className={styles.itemValue}>{data?.coCommission?.label || NoSetText}</span>
          </Col>
        </Row>
        <Row gutter={60} className={styles.rowWrap}>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>担保大宗交易：</span>
            <span className={styles.itemValue}>{data?.ddCommission?.label || NoSetText}</span>
          </Col>
          <Col span={8} className={styles.itemCol}>
            <span className={styles.itemName}>担保权证：</span>
            <span className={styles.itemValue}>{data?.dqCommission?.label || NoSetText}</span>
          </Col>
        </Row>
      </div>
      <div className={styles.contentInfo}>
        <BlockHeader title="账户最低收费设置" />
        <Row className={styles.rowWrap}>
          <Col span={12} className={styles.itemCol}>
            <span className={styles.itemName}>是否定制化设置最低收费：</span>
            <span className={styles.itemValue}>{YOrN[data?.multiMiniChargeSet]}</span>
          </Col>
        </Row>
        {/* 【是否定制化设置最低收费=否】展示此模块 */}
        <IFWrap when={data?.multiMiniChargeSet === YesOrNo.NO}>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户收取最低费用：</span>
              <span className={styles.itemValue}>{data?.normalMinimumCharge?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户收取最低费用：</span>
              <span className={styles.itemValue}>{data?.creditMinimumCharge?.label || NoSetText}</span>
            </Col>
          </Row>
        </IFWrap>
        {/* 【是否定制化设置最低收费=是】展示此模块 */}
        <IFWrap when={data?.multiMiniChargeSet === YesOrNo.YES}>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户股票：</span>
              <span className={styles.itemValue}>{data?.gpMinimumCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户股票：</span>
              <span className={styles.itemValue}>{data?.creditgpMinimumCommission?.label || NoSetText}</span>
            </Col>
          </Row>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户场内基金：</span>
              <span className={styles.itemValue}>{data?.onExchangeFundCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户场内基金：</span>
              <span className={styles.itemValue}>{data?.creditOnExchangeFundCommission?.label || NoSetText}</span>
            </Col>
          </Row>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户上海债券（不含可转债）：</span>
              <span className={styles.itemValue}>{data?.shNonConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户上海债券（不含可转债）：</span>
              <span className={styles.itemValue}>{data?.shCreditNonConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
          </Row>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户上海可转债：</span>
              <span className={styles.itemValue}>{data?.shConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户上海可转债：</span>
              <span className={styles.itemValue}>{data?.shCreditConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
          </Row>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户深圳债券（不含可转债）：</span>
              <span className={styles.itemValue}>{data?.szNonConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户深圳债券（不含可转债）：</span>
              <span className={styles.itemValue}>{data?.szCreditNonConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
          </Row>
          <Row className={styles.rowWrap}>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>普通账户深圳可转债：</span>
              <span className={styles.itemValue}>{data?.szConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
            <Col span={12} className={styles.itemCol}>
              <span className={styles.itemName}>信用账户深圳可转债：</span>
              <span className={styles.itemValue}>{data?.szCreditConvertibleBondsCommission?.label || NoSetText}</span>
            </Col>
          </Row>
        </IFWrap>
      </div>
    </div>
  );
}

StandardCommissionContent.propTypes = {
  data: PropTypes.object.isRequired,
};

export default React.memo(StandardCommissionContent);
