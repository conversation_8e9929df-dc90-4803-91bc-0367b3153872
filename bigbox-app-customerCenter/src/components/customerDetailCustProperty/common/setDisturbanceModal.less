/* stylelint-disable font-family-no-missing-generic-family-keyword */
.setDisturbanceModalWrap {
  height: 592px;
  .modalContentWrap {
    padding-top: 20px;
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
      .@{ant-prefix}-form {
        .@{ant-prefix}-row {
          margin-bottom: 10px;
        }
        .@{ant-prefix}-form-item {
          font-family: "Microsoft YaHei";
          font-size: 14px;
          label {
            font-family: "Microsoft YaHei";
            font-size: 14px;
            color: #666;
          }
          .@{ant-prefix}-form-item-label {
            width: 95px;
          }
          .@{ant-prefix}-form-explain {
            color: #f5222d;
          }
          .@{ant-prefix}-checkbox-group-item {
            margin-right: 0;
          }
          .@{ant-prefix}-time-picker-icon {
            right: 11px;
          }
          .@{ant-prefix}-checkbox-wrapper + .@{ant-prefix}-checkbox-wrapper {
            margin-left: 11px;
          }
          .@{ant-prefix}-input,
          .@{ant-prefix}-select-selection {
            border-radius: 2px;
            width: 200px;
          }
          .@{ant-prefix}-select-disabled {
            color: #999;
          }
          textarea.@{ant-prefix}-input {
            width: 537px;
            height: 50px;
          }
          .@{ant-prefix}-time-picker {
            font-size: 14px;
            width: 130px;
          }
        }
      }
      .@{ant-prefix}-form-inline {
        .@{ant-prefix}-form-item,
        .@{ant-prefix}-form-item-with-help {
          margin-bottom: 0;
          margin-right: 0;
        }
      }
    }
    .switchArea {
      margin-bottom: 10px;
      .switchBox {
        margin-right: 40px;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #666;

        .switch {
          margin-left: 15px;
        }
      }
    }
    .disturbanceContent {
      width: 720px;
      padding: 20px 0;
      background: #f7f7f7;
      border-radius: 2px;
      .alertWrapper {
        margin: -10px 20px 5px;
      }
      &::before {
        content: "";
        width: 14px;
        height: 14px;
        background: #f7f7f7;
        position: absolute;
        transform: rotate(-315deg);
        left: 68px;
        top: 56px;
      }
      .timeSplite {
        margin: 0 14px;
      }
      .disabled {
        color: #999;
      }
      .longValue {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
      .requiredIcon {
        &::before {
          content: "*";
          position: absolute;
          display: inline-block;
          color: #f5222d;
          font-size: 12px;
          font-family: SimSun, sans-serif;
          top: 9px;
          left: 2px;
          line-height: 1;
        }
      }
    }
  }
}
