/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-11-21 09:35:09
 * @LastEditors: yanfaping
 * @LastEditTime: 2021-06-30 13:34:39
 * @Description: 服务订购
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import moment from 'moment';

import { ReactComponent as AscIcon } from '@/static/svg/asc.svg';
import { ReactComponent as DescIcon } from '@/static/svg/desc.svg';
import { ReactComponent as NormalIcon } from '@/static/svg/normal.svg';

import ToolTip from '@/components/common/Tooltip';
import Table from '@/components/common/table';
import logable, { logCommon } from '@/decorators/logable';
import { openExternalPage } from '@/helper';
import {
  SERVICE_ORDER_TABLE_COLUMNS,
  DATE_FORMATE_STR,
  SERVICE_ORDER_PLACE_HOLDER,
  // FLAG_ONLINE,
  // FLAG_SUCCESS,
  // FLAG_NORMAL,
} from './config';
import {
  displayEmptyCell,
  isEmptyCell,
  getPage,
} from './utils';

import styles from './serviceOrder.less';

// const warning = Modal.warning;

export default class ServiceOrder extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
    serviceOrderData: PropTypes.object.isRequired,
    // queryCustCanChangeCommission: PropTypes.func.isRequired,
    queryServiceOrderData: PropTypes.func.isRequired,
  }

  static contextTypes = {
    push: PropTypes.func.isRequired,
    recordCustomerLog: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      sortType: 'DESC',
      sortValue: 'startDt',
    };
  }

  componentDidMount() {
    this.getServiceOrderData();
  }

  @autobind
  getColumns(columns) {
    const newColumns = _.map(columns, (column) => {
      const { dataIndex, needEllipse } = column;
      if (dataIndex === 'intrRate') {
        // 渲染佣金
        return this.getIntrRateColumn(column);
      }
      if (dataIndex === 'startDt' || dataIndex === 'endDt') {
        // 开始日期、结束日期需要排序
        return this.getDateSortColumn(column);
      }
      if (needEllipse) {
        return this.getWordColumn(column);
      }
      return column;
    });

    return newColumns;
  }

  // 渲染佣金率列，因为佣金率列后端有可能会给字符串
  @autobind
  getIntrRateColumn(column) {
    return {
      ...column,
      render(text, record) {
        if (isEmptyCell(text, record)) {
          return displayEmptyCell(text, record);
        }
        if (_.isNumber(text)) {
          return text;
        }
        return text;
      }
    };
  }

  // 渲染日期列，因为日期列需要在头部添加排序功能
  @autobind
  getDateSortColumn(column) {
    const { sortType, sortValue } = this.state;
    // 默认排序图标
    let direImg = <NormalIcon />;
    if (sortValue === column.dataIndex) {
      direImg = sortType === 'DESC' ? <DescIcon /> : <AscIcon />;
    }

    return {
      ...column,
      title: (
        <span className={styles.sortContent}>
          {column.title}
          {direImg}
        </span>
      ),
      render(text, record) {
        if (isEmptyCell(text, record)) {
          return displayEmptyCell(text, record);
        }
        return moment(text).format(DATE_FORMATE_STR);
      },
      onHeaderCell: () => ({
        onClick: () => this.handleSortChange(column),
      })
    };
  }

  // 需要展示提示框的
  @autobind
  getWordColumn(column) {
    return {
      ...column,
      render(text, record) {
        if (isEmptyCell(text, record)) {
          return displayEmptyCell(text, record);
        }
        return (
          <ToolTip title={text} placement="topLeft">
            <div className={styles.textEllipse}>{text}</div>
          </ToolTip>
        );
      },
    };
  }

  @autobind
  getServiceOrderData(options = {}) {
    const { sortType, sortValue } = this.state;
    const { location: { query: { custId } } } = this.props;
    if (custId) {
      this.props.queryServiceOrderData({
        custId,
        sortType,
        sortValue,
        pageNum: 1,
        pageSize: 10,
        ...options,
      });
    }
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '产品订单-服务订购-页码',
      value: '$args[0]'
    },
  })
  handlePageChange(current) {
    this.getServiceOrderData({
      pageNum: current,
    });
  }

  @autobind
  handleSortChange({ dataIndex }) {
    if (dataIndex === 'startDt') {
      this.handleStartDateSort();
    } else {
      this.handleEndDateSort();
    }
  }

  // 点击开始日期进行排序
  @autobind
  handleStartDateSort() {
    // 因为开始日期和结束日期的排序是互斥的
    const { sortValue, sortType } = this.state;
    let nextSortType = 'DESC';
    if (sortValue === 'startDt') {
      nextSortType = sortType === 'DESC' ? 'ASC' : 'DESC';
    }
    const nextState = {
      sortValue: 'startDt',
      sortType: nextSortType,
    };
    this.setState(nextState, this.getServiceOrderData);

    logCommon({
      type: 'Click',
      payload: {
        name: '产品订单-服务订购-开始日期-排序',
        value: nextSortType === 'DESC' ? '降序' : '升序',
      },
    });
  }

  // 点击结束日期进行排序
  @autobind
  handleEndDateSort() {
    // 因为开始日期和结束日期的排序是互斥的
    const { sortValue, sortType } = this.state;
    let nextSortType = 'DESC';
    if (sortValue === 'endDt') {
      nextSortType = sortType === 'DESC' ? 'ASC' : 'DESC';
    }
    const nextState = {
      sortValue: 'endDt',
      sortType: nextSortType,
    };
    this.setState(nextState, this.getServiceOrderData);

    logCommon({
      type: 'Click',
      payload: {
        name: '产品订单-服务订购-结束日期-排序',
        value: nextSortType === 'DESC' ? '降序' : '升序',
      },
    });
  }

  // 线上调佣校验不通过的话，跳转到新建佣金调整页面，调佣方式默认线下只读
  // @autobind
  // handleCommissionWarningConfirm(isOnline, custId) {
  //   if (isOnline) {
  //     // Y表示禁用新建佣金调整页面的调佣方式
  //     const commissionWayDisable = 'Y';
  //     this.handleJumpSingleCommissionNew(custId, commissionWayDisable);
  //   }
  // }

  // @autobind
  // getValidMsg(res) {
  //   // flag值有3个
  //   // -1 : 表示非线上调佣校验结果失败
  //   // -2 : 线上调佣校验结果失败
  //   // 0 : 校验成功
  //   const { flag, msg = '' } = res;
  //   // 是否是线上调佣校验结果
  //   const isOnline = flag === FLAG_ONLINE;
  //   // 默认提示语
  //   const defaultMessage = '网络错误，请稍后重试';
  //   let validMsg = defaultMessage;
  //   switch (flag) {
  //     case FLAG_NORMAL:
  //       validMsg = msg;
  //       break;
  //     case FLAG_ONLINE:
  //       validMsg = `${msg}，只能进行线下调佣，不支持线上调佣方式`;
  //       break;
  //     default:
  //       break;
  //   }
  //   return { validMsg, isOnline };
  // }

  @autobind
  handleJumpSingleCommissionNew(custId) {
    openExternalPage({
      pathname: '/businessApplyment/commissionNew/singleCommissionNew',
      query: {
        custId,
      }
    }, this.context.push);
  }

  render() {
    const {
      serviceOrderData: {
        productList = [],
        page = {},
      },
    } = this.props;
    const isEmptyData = _.isEmpty(productList);
    let pagination = false;
    if (!isEmptyData) {
      const pageProps = getPage(page);
      pagination = {
        ...pageProps,
        onChange: this.handlePageChange,
      };
    }

    const columns = this.getColumns(SERVICE_ORDER_TABLE_COLUMNS);
    return (
      <div className={styles.tradeOrderFlowWrap}>
        <Table
          useNewUI
          isNeedEmptyRow
          withBorder
          rowNumber={10}
          rowKey="name"
          columns={columns}
          dataSource={productList}
          pagination={pagination}
          placeHolderImageProps={SERVICE_ORDER_PLACE_HOLDER}
          expandColumnProps={{ hasExpand: true }}
        />
      </div>
    );
  }
}
