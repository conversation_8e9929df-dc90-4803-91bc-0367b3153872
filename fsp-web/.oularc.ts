import { defineConfig } from '@oula/oula'
const fs = require("fs");
const path = require("path");
const appDirectory = fs.realpathSync(process.cwd());
const resolve = (relativePath) => path.resolve(appDirectory, relativePath);
const pack = require("./package.json");

const { EMAS_BUILD_ID } = process.env;

const TARGET_URL = "http://168.64.33.185:80";
export default defineConfig({
    bundlerConfig: {
        source: {
            alias: {
              '@/components': resolve('src/components'),
              '@/config': resolve('src/config'),
              '@/helper': resolve('src/helper'),
              '@/decorators': resolve('src/decorators'),
              '@/hooks': resolve('src/hooks'),
              '@/utils': resolve('src/utils'),
              '@/routes': resolve('src/routes'),
              'htsc/static': resolve('static'),
              '@/css': resolve('src/css'),
              '@/middlewares': resolve('src/middlewares'),
              '@/api': resolve('src/api'),
              '@/layouts': resolve('src/layouts'),
              '@/newUI': resolve('src/components/common/newUI'),
              '@ant-design/icons/lib/dist$': resolve('src/icons.js'),
            },
            transformImport:[
              {
                libraryName: 'lodash',
                customName: 'lodash/{{ member }}',
              },
              {
                libraryName: "antd",
                style: true,
              }
            ],
            define: {
              PROJECT_JOBID: `"${EMAS_BUILD_ID || pack.version}"`,
            },
          },
        output: {
          copy: [{ from: path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps'), to: 'cmaps' }],
          externals: {
            'react': 'React',
            'react-dom': 'ReactDOM',
            '@lego/bigbox-utils': 'bigboxUtils',
            'echarts': 'echarts',
            'xlsx': 'XLSX',
            '@crm/feedbacks': 'crmFeedback',
            'canvas': 'canvas',
            '@crm/app-utils': 'appUtils',
          },
          overrideBrowserslist: ["last 2 versions", "ie >= 10", "chrome >= 45"],
        },
        server: {
          proxy: {
            "/fspa/mcrm/api": TARGET_URL,
            "/fspa/customerCenter/api": TARGET_URL,
            "/fspa/strategy/api":TARGET_URL,
            "/fspa/aorta/biz/api": {
              target: 'http://168.63.25.155:8082',
              pathRewrite: { "/fspa/aorta/biz/api": "/aorta/biz/api" },
            },
            "/fspa/aorta/user/api": {
              target: 'http://168.63.69.175:8081', // 后端部署61的路径
              pathRewrite: { "/fspa/aorta/user/api": "/aorta/user/api" },
            },
            "/fspa/aorta/dmz/api": TARGET_URL,
            "/goapi/FSP": 'http://mock.htsc'
          },
        },
        tools:{
          less:{
            lessOptions: {
              math: 'always',
            },
          },
          rspack(config,{addRules}){
            addRules([
              {
                test: /\.(xls|docx|doc|msi|pdf)$/,
                // 将资源转换为单独的文件，并且导出产物地址
                type: 'asset/resource',
              },
            ]);
          }
        },
    },
    bigtoolsConfig:{
        useBabel: true,
    },
    checkSyntax: {
      exclude: [/.html$/,/pdf.worker.min.js/,/node_modules\/pdfjs-dist\/build\/pdf.js/],
    }
})
