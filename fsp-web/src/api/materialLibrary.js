/**
 * @Author: liji<PERSON>jing
 * @Descripter: 自定义海报配置
 * @Date: 2022-04-25 17:09:53
 * @Last Modified by: weiting
 * @Last Modified time: 2022-05-05 14:17:51
 */

export default function materialLibrary(api) {
  return {
    // 海报列表
    queryPosterList: (query) => api.post('/fspa/strategy/api/desktop/material/standardized/pc/StandardizedMaterialQuery/listStandardizedMaterial', query, { isFullUrl: true }),
    // 保存海报
    savePosterList: (query) => api.post('/fspa/strategy/api/desktop/material/standardized/pc/StandardizedMaterialCmd/batchSaveStandardizedMaterial', query, { isFullUrl: true }),
  };
}
