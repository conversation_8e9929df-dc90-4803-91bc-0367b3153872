/*
 * @Author: sunweibin
 * @Date: 2021-06-28 17:09:26
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-08 10:24:03
 * @description 批量佣金调整-添加客户组件
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import { Button, Icon as AntdIcon } from 'antd';
import { connect } from 'dva';

import Tooltip from '@/components/common/Tooltip';
import logable, { logCommon } from '@/decorators/logable';
import Table from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import confirm from '@/components/common/newUI/confirm';
import AutoComplete from '@/components/common/similarAutoComplete';
import IfWrap from '@/components/common/IFWrap';
import Icon from '@/components/common/Icon';
import { emp, dva } from '@/helper';
import { request } from '@/config';
import BusinessExcelUploader from '@/components/common/BusinessExcelUploader';
import CommissionXLS from './commission.xls';
import {
  CUSTOMER_PLACEHOLDER_PROPS,
  CUSTOMER_COLUMNS,
  DEFAULT_PAGE_SIZE,
  TEXT_Y,
  HAS_CHECK,
  HAS_REJECTED,
  HAS_NEW,
  LIMIT_COUNT,
  ONLINE_TIP,
  // NEED_INVEST_CONTRACT_CHECK_KEYS
} from './config';
import styles from './index.less';

// 用于找到select类组件渲染option时父级容器的方法,以解决在弹窗里页面滚动，option随页面滚动的问题
const getPopupContainerFunction = () => document.querySelector(`.${styles.searchSelectArea}`);

const mapStateToProps = () => ({
});

const mapDispatchToProps = {
  getCanApplyCustList: dva.generateEffect('batchCommission/getCanApplyCustList', { loading: false }),
  validateSingleCustomer: dva.generateEffect('batchCommission/validateSingleCustomer'),
  terminalOrderFlow: dva.generateEffect('batchCommission/terminalOrderFlow'),
  changeOrderStatus: dva.generateEffect('batchCommission/changeOrderStatus'),
  getValidateCustomerInfo: dva.generateEffect('batchCommission/getValidateCustomerInfo'),
  validateMultiCustomer: dva.generateEffect('batchCommission/validateMultiCustomer'),
  queryValidateProgress: dva.generateEffect('batchCommission/queryValidateProgress'),
};

@connect(mapStateToProps, mapDispatchToProps, null, { withRef: true })
export default class BatchAddCustomer extends PureComponent {
  static propTypes = {
    // 是否禁用相关移除、添加、导入功能
    disabled: PropTypes.bool.isRequired,
    // 关键字搜索客户
    getCanApplyCustList: PropTypes.func.isRequired,
    // 校验单个客户
    validateSingleCustomer: PropTypes.func.isRequired,
    // 佣金调整校验客户如果有被驳回订单，终止订单
    terminalOrderFlow: PropTypes.func.isRequired,
    // 佣金调整校验客户如果有新建订单，修改订单状态
    changeOrderStatus: PropTypes.func.isRequired,
    // 获取校验后的客户信息
    getValidateCustomerInfo: PropTypes.func.isRequired,
    // 发送请求时的其他参数
    otherPayload: PropTypes.object.isRequired,
    // 执行方式新增(是否为智能外呼)
    executionStyle: PropTypes.string.isRequired,
    // 将添加的客户数据传递给父组件
    onPassData: PropTypes.func.isRequired,
    // 批量客户添加校验
    validateMultiCustomer: PropTypes.func.isRequired,
    // 查询批量导入客户的校验进度
    queryValidateProgress: PropTypes.func.isRequired,
    // 调佣方式
    adjustCommissionWayCode: PropTypes.string.isRequired,
    // 其他佣金费率
    otherCommissions: PropTypes.object.isRequired,
  }

  constructor(props) {
    super(props);
    // 是否添加了鼠标处理事件
    this.isAddedMouseHandle = false;
    this.state = {
      popoverDisplay: 'none',
      // 用于批量/单客户校验后查询客户信息使用的参数
      // 1. 初次校验客户时传递空，后端返回实际的batchId，将该batchId保存下来用于下一次
      // 2. 每一次使用批量校验客户时都传空，然后等待后端接口返回实际 batchId, 供下一次单个客户校验使用
      batchId: '',
      // 搜搜客户返回的客户列表
      searchList: [],
      // 搜索后选择的客户
      customer: null,
      // 添加到表格中的所有客户ID列表,用于后续Table展示时查询客户信息数据使用
      custIDList: [],
      // 当前第几页
      pageNum: 1,
      // 用于展示当前页码下的客户信息
      currentShowCustList: [],
      // 批量查询导入客户校验进度的数据
      processData: {},
      // 选中的客户
      selectedRows: [],
      // 批量上传的进度弹框
      uploadPopoverVisible: false,
    };

    this.queryCustomerComponentRef = React.createRef();
  }

  componentDidUpdate() {
    this.checkBoxDOM = document.querySelectorAll('.ant-table-selection')[0];  // eslint-disable-line

    if (this.checkBoxDOM && !this.isAddedMouseHandle) {
      this.checkBoxDOM.addEventListener('mouseenter', this.handleCheckBoxMouseEnter, false);
      this.checkBoxDOM.addEventListener('mouseleave', this.handleCheckBoxMouseLeave, false);
      this.isAddedMouseHandle = true;
    }
  }

  componentWillUnmount() {
    if (this.checkBoxDOM) {
      this.checkBoxDOM.removeEventListener('mouseenter', this.handleCheckBoxMouseEnter, false);
      this.checkBoxDOM.removeEventListener('mouseleave', this.handleCheckBoxMouseLeave, false);
      this.isAddedMouseHandle = false;
    }
  }

  // 头部 checkbox 鼠标移入
  @autobind
  handleCheckBoxMouseEnter() {
    this.setState({
      popoverDisplay: 'block',
    });
  }

  // 头部 checkbox 鼠标移出
  @autobind
  handleCheckBoxMouseLeave() {
    this.setState({
      popoverDisplay: 'none',
    });
  }

  @autobind
  handleSearchCustomer(keyword) {
    this.props.getCanApplyCustList({
      keyword,
      type: '02', // 表示【服务订购】页面
      subType: '0202', // 表示【批量佣金调整】
      postnId: emp.getPstnId(),
    }).then((searchList) => this.setState({ searchList }));
  }

  // 校验不通过，有未完成、新建、被驳回状态的订单时，提示弹框
  @autobind
  fail2Validate(obj) {
    const {
      customer,
      params,
      content,
      cancelVisible = true,
    } = obj;
    confirm({
      title: '提示',
      content,
      okText: '确定',
      onOk: () => { this.handleOKAfterValidate(customer, params); },
      cancelVisible
    });
  }

  // 有未完成、新建、被驳回状态的订单时，点击提示弹框确认按钮
  @autobind
  handleOKAfterValidate(customer, otherParams) {
    const {
      isUnfinish,
      isRejected,
      isNew,
      brokerNumber,
    } = customer;
    const params = {
      custId: brokerNumber,
      ...otherParams
    };
    // 是否是fsp流程
    const isFspFlow = otherParams.fspFlow;
    // 选择的客户，有未完成订单，点击确定清空输入框内容，不做跳转
    if (isUnfinish) {
      this.clearInputValue();
      return;
    }
    // 有被驳回订单，点击确定,如果是fsp流程则掉接口终止订单，否则就更改状态
    if (isRejected) {
      if (isFspFlow) {
        this.props.terminalOrderFlow(params);
      } else {
        this.props.changeOrderStatus(params);
      }
      return;
    }
    // 有新建订单，点击确定则掉接口修改“新建”状态为“取消”，并可继续发起调佣流程
    if (isNew) {
      this.props.changeOrderStatus(params);
    }
  }

  @autobind
  clearCustList() {
    this.setState({
      batchId: '',
      searchList: [],
      customer: null,
      custIDList: [],
      pageNum: 1,
      currentShowCustList: [],
      processData: {},
      selectedRows: [],
    });
  }

  @autobind
  @logable({
    type: 'Click',
    payload: {
      name: '新建批量佣金调整-客户-移除'
    }
  })
  handleDeleteCustomer() {
    const { custIDList, selectedRows } = this.state;

    if (_.isEmpty(selectedRows)) return;

    const selectedCust = _.map(selectedRows, (item) => item.custId);

    const newList = _.filter(custIDList, (item) => !_.includes(selectedCust, item));

    this.setState({
      custIDList: newList,
      selectedRows: [],
      pageNum: 1,
    }, this.doAfterDeleteCustomer);
  }

  @autobind
  doAfterDeleteCustomer() {
    // 如果删除到没有客户的时候，清空已校验的客户数据
    const { custIDList } = this.state;
    if (!_.isEmpty(custIDList)) {
      this.queryValidateCustomer();
    }
    // 如果删除到没有客户的时候，应该把当前table展示的数据也给清空
    this.setState({
      currentShowCustList: [],
    });
    this.props.onPassData(custIDList);
  }

  // 选择客户
  @autobind
  handleSelectCustomer(customer) {
    this.setState({
      customer,
    });
    logCommon({
      type: 'DropdownSelect',
      payload: {
        name: '新建批量佣金调整-选择客户',
        value: JSON.stringify(customer),
      }
    });
  }

  @autobind
  checkSingleCustomer() {
    const {
      customer,
      custIDList,
    } = this.state;
    const {
      otherPayload: {
        newCommission
      },
    } = this.props;
    // 判断是否已经存在改用户
    const isExist = _.find(custIDList, (item) => item === customer?.brokerNumber);
    let tip = '';
    // NOTE: 因为已经不存在产品了所以，此处先删除
    // if (_.isEmpty(prodCode)) {
    //   tip = '请选择目标产品';
    // }
    if (_.isEmpty(newCommission)) {
      tip = '请选择目标股基佣金率';
    }
    if (_.isEmpty(customer)) {
      tip = '请先选择客户';
    }
    // 如果存在，则不给添加
    if (isExist) {
      tip = '客户已经存在';
    }
    // 如果客户列表中已经有 100个客户，则不让再添加
    if (custIDList.length >= LIMIT_COUNT) {
      tip = `客户数量不能超过${LIMIT_COUNT}个`;
    }
    return tip;
  }

  // 添加客户
  @autobind
  @logable({ type: 'Click', payload: { name: '新建批量佣金调整-添加客户' } })
  handleAddCustomer() {
    const { customer, batchId } = this.state;
    const result = this.checkSingleCustomer();
    if (result !== '') {
      confirm({
        title: '提示',
        content: result,
        cancelVisible: false,
      });
      return;
    }

    const {
      otherPayload,
      executionStyle,
      adjustCommissionWayCode,
      otherCommissions,
    } = this.props;
    this.props.validateSingleCustomer({
      ...otherPayload,
      batchId,
      type: 'single',
      custs: [{
        custId: customer?.brokerNumber,
        custName: customer.custName,
      }],
      orgId: emp.getOrgId(),
      executionStyleCode: executionStyle,
      // TODO: 目前因为本页面只会在有【新功能体验岗】权限下使用，因此此处直接给 true，
      // TODO: 后期放开灰度，需要研究是否前后端需要统一修改
      hasNewFlow: true,
      // 调佣方式
      adjustCommissionWayCode,
      commissionDTO: { ...otherCommissions, basicCommission: otherCommissions?.newCommission },
    }).then(this.doAfterValidateSingle);
  }

  // @autobind
  // checkIsSetGJComms() {
  //   const { otherCommissions } = this.props;
  //   if (_.isEmpty(otherCommissions)) {
  //     return false;
  //   }

  //   // 判断是否设置了【股基】【信用股基】【担保股基】费率
  //   const hasGjComms = _.some(NEED_INVEST_CONTRACT_CHECK_KEYS,
  //     (key) => !_.isEmpty(otherCommissions[key]));
  //   return hasGjComms;
  // }

  @autobind
  doAfterValidateSingle(validResult) {
    const { result, msg } = validResult;
    // if (validResult?.result?.hasMutexError) {
    //   confirm({
    //     title: '提示',
    //     content: validResult?.result?.mutexErrorMsg,
    //     okText: '确定',
    //     onOk: () => false,
    //     cancelVisible: false,
    //   });
    //   return;
    // }
    // 如果接口调用失败，将错误抛出
    if (msg !== 'OK') {
      confirm({
        title: '提示',
        content: '校验客户失败',
        cancelVisible: false,
      });
      return;
    }
    const {
      hasOrder, // 是否有待审批订单
      orderStatus, // 订单状态
      fspFlow, // 是否是fsp流程
      orderId, // 订单id
      flowId, // 流程id
      flowStarter, // 流程发起者工号
      errorMsg,
      batchId, // 后端返回的 batchId, 用于下一次使用
      pass,
      custId,
      smartCallValidFlag,
      validateMsg,
      hasAgrFlow,
      // hasNewAgrFlow,
      hasTerminateCommissionContract,
    } = result;
    const { customer } = this.state;
    const params = {
      fspFlow,
      orderId,
      flowId,
      flowStarter
    };

    // 2024-01-30 佣金调整增加新投顾签约的校验
    // 有在途的或生效中的老签约流程，均不允许发起调佣
    if (hasAgrFlow === TEXT_Y) {
      confirm({
        title: '提示',
        content: '客户有在途或者签约流程投顾协议，不允许进行佣金调整。',
        okText: '确定',
        cancelVisible: false,
      });
      return;
    }

    // 2024-01-30 佣金调整增加新投顾签约的校验
    // 2025-04-02 去掉这个校验，有在途的新投顾签约报错信息在errMsg中会体现
    // 判断是否选择了【目标股基】【信用股基】【担保股基】且 有在途的或生效中的新签约流程，均不允许发起调佣
    // if (hasNewAgrFlow === TEXT_Y && this.checkIsSetGJComms()) {
    //   confirm({
    //     title: '提示',
    //     content: '客户有在途或者签约流程投顾协议，不允许调整股基',
    //     okText: '确定',
    //     cancelVisible: false,
    //   });
    //   return;
    // }

    // 2024-05-09 佣金调整增加新投顾解约的校验
    // 有在途的或生效中的新投顾解约流程，均不允许发起调佣
    if (hasTerminateCommissionContract === TEXT_Y) {
      confirm({
        title: '提示',
        content: '该客户有在途的投顾解约流程，请在解约流程办结后再发起申请。',
        okText: '确定',
        cancelVisible: false,
      });
      return;
    }

    // 批量佣金调整有待审批订单时
    if (hasOrder === TEXT_Y && orderStatus === HAS_CHECK) {
      this.fail2Validate({
        customer: {
          ...customer,
          isUnfinish: true,
        },
        params,
        content: validateMsg,
        cancelVisible: false,
      });
      return;
    }
    // 批量佣金调整有被驳回订单时
    if (hasOrder === TEXT_Y && orderStatus === HAS_REJECTED) {
      this.fail2Validate({
        customer: {
          ...customer,
          isRejected: true,
        },
        params,
        content: '此客户名下有被驳回的佣金调整订单，只有终止被驳回的订单后才可以继续发起调佣申请，点击“确定”终止被驳回的订单！',
      });
      return;
    }
    // 批量佣金调整有新建订单时
    if (hasOrder === TEXT_Y && orderStatus === HAS_NEW) {
      this.fail2Validate({
        params,
        customer: {
          ...customer,
          isNew: true,
        },
        content: '此客户名下有新建状态的订单，点击“确定”关闭新建状态的订单重新发起新的调佣流程！',
      });
      return;
    }
    // 如果校验失败，提示对应的信息
    if (errorMsg) {
      // 如果是智能外呼的客户校验错误不需要拼接客户名称
      if (smartCallValidFlag) {
        confirm({
          title: '提示',
          content: errorMsg,
          cancelVisible: false,
        });
        return;
      }
      confirm({
        title: '提示',
        content: `${customer.custName}${errorMsg}`,
        cancelVisible: false,
      });
      return;
    }
    // 如果校验通过，则将 custId 添加到本地列表中
    if (pass) {
      this.updateStateData({
        custIds: [custId],
        batchId,
      });
      // 单个客户校验通过后，将输入框中的内容清空
      const node = this.queryCustomerComponentRef.current;
      if (node) {
        node.clearValue();
      }
    }
  }

  // 清空搜索客户输入框的值
  @autobind
  clearInputValue() {
    const inputRef = this.queryCustomerComponentRef.current;
    if (inputRef) {
      inputRef.clearValue();
    }
  }

  @autobind
  updateStateData(validateProgress) {
    const {
      custIds, // 客户 ID 集合
      batchId, // 批次 ID
    } = validateProgress;
    if (!_.isEmpty(custIds)) {
      this.setState((prevState) => ({
        batchId, // 保存下来给与下一次使用
        customer: {},
        custIDList: [...prevState.custIDList, ...custIds],
      }), () => {
        const { custIDList } = this.state;
        this.props.onPassData(custIDList);
        this.queryValidateCustomer();
      });
    }
  }

  @autobind
  updateBatchImportData(validateProgress) {
    const {
      custIds, // 客户 ID 集合
      batchId, // 批次 ID
    } = validateProgress;
    this.setState({
      batchId, // 保存下来给与下一次使用
      custIDList: custIds || [],
    }, () => {
      const { custIDList } = this.state;
      this.props.onPassData(custIDList);
      this.queryValidateCustomer();
    });
  }

  // 上传完成后的回调
  @autobind
  handleValidateFinish(progress) {
    const {
      custIds,
      total,
      unPass,
      failExcelPath,
    } = progress;
    // 全部校验通过，根据单客户或批量添加现实不同，更新数据
    if (_.size(custIds) === total) {
      confirm({
        title: '提示',
        content: `导入成功，共导入${total}条数据`,
        okText: '确定',
        onOk: () => this.updateBatchImportData(progress),
        cancelVisible: false,
      });
    } else {
      // 如果返回的客户数据个数不等于总条数，则数据有校验失败的，显示 confirm，确定后更新数据
      const content = (
        <div className={styles.confirmContent}>
          <h3>共添加{total}条数据，有{unPass}条数据没有通过校验未导入到列表中，错误原因详见附件回执文件：</h3>
          <h4>下载附件</h4>
          <Icon type="fujian2" />
          <a href={`${request.URL_PREFIX}${failExcelPath}`}>
            异常客户详情.xlsx
          </a>
        </div>
      );
      confirm({
        title: '提示',
        content,
        okText: '确定',
        onOk: () => this.updateBatchImportData(progress),
        cancelVisible: false,
      });
    }

    this.setState({
      processData: {},
    });
  }

  @autobind
  handleQueryValidProcess() {
    const { batchId } = this.state;
    return this.props.queryValidateProgress({ batchId })
      .then((processData) => this.setState({ processData }));
  }

  // 选择客户
  @autobind
  @logable({
    type: 'Checkbox',
    payload: {
      name: '选择客户',
      value: '$args[0].custName',
    },
  })
  handleSelectChange(record, selected) {
    const { selectedRows } = this.state;
    // 选中的 row 数组
    let newSelectedRows = [...selectedRows];
    if (selected) {
      newSelectedRows.push(record);
    } else {
      newSelectedRows = _.filter(newSelectedRows, (o) => o.custNumber !== record.custNumber);
    }
    this.setState({
      selectedRows: newSelectedRows,
    });
  }

  @autobind
  @logable({ type: 'Click', payload: { name: '新建批量客户-全选/全不选客户' } })
  handleSelectAll(selected, selectedAllRows, changeRows) {
    const { selectedRows } = this.state;
    const changeRowKeys = _.map(changeRows, 'custNumber');
    let newSelectedRows = [...selectedRows];
    if (selected) {
      newSelectedRows.push(...changeRows);
    } else {
      newSelectedRows = _.filter(newSelectedRows, (o) => !_.includes(changeRowKeys, o.custNumber));
    }
    this.setState({
      selectedRows: newSelectedRows,
    });
  }

  // 客户翻页事件
  @autobind
  handleCustPageChange(pageNum) {
    this.setState({
      pageNum,
    }, this.queryValidateCustomer);
  }

  @autobind
  queryValidateCustomer() {
    const {
      custIDList,
      pageNum,
      batchId,
    } = this.state;
    // 按照每页条数以及处于第几页来取具体的客户 ID
    const chunkCustomerList = _.chunk(custIDList, DEFAULT_PAGE_SIZE);
    const custIds = chunkCustomerList[pageNum - 1];
    // 因为客户校验接口只返回custID,因此需要在校验完成后获取下校验客户的其他信息
    this.props.getValidateCustomerInfo({
      batchId,
      custIds,
    }).then((currentShowCustList) => this.setState({ currentShowCustList }));
  }

  // 批量导入客户的校验
  @autobind
  handleMultiValidator(params) {
    const { adjustCommissionWayCode, otherCommissions } = this.props;
    const newParams = {
      ...params,
      // TODO: 目前因为本页面只会在有【新功能体验岗】权限下使用，因此此处直接给 true，
      // TODO: 后期放开灰度，需要研究是否前后端需要统一修改
      hasNewFlow: true,
      // 调佣方式
      adjustCommissionWayCode,
      commissionDTO: { ...otherCommissions, basicCommission: otherCommissions?.newCommission },
    };
    return this.props.validateMultiCustomer(newParams)
      .then((res) => {
        this.setState({ batchId: res?.batchId });
        return res || {};
      });
  }

  @autobind
  handleBeforeUpload() {
    this.setState({
      uploadPopoverVisible: false,
    });
  }

  // @autobind
  // renderValidateModal(res) {
  //   const url = res?.mutexErrorFileId
  // eslint-disable-next-line max-len
  //     ? `${request.URL_PREFIX}/aorta/dmz/api/storage/s3/download?attachId=${res?.mutexErrorFileId}
  // &filename=异常客户详情.xlsx&empId=${emp.getId()}` : '';
  //   confirm({
  //     title: '提示',
  //     content: (
  //       <div className={styles.mutexError}>
  //         <div className={styles.mutexErrorMsg}>{res?.mutexErrorMsg}</div>
  //         <div className={styles.mutexErrorFile}>
  //           <Icon type="fujian2" />
  //           <a href={url}>异常客户详情.xlsx</a>
  //         </div>
  //       </div>
  //     ),
  //     okText: '确定',
  //     onOk: () => false,
  //     cancelVisible: false,
  //   });
  // }

  render() {
    const {
      otherPayload,
      disabled,
      // 当前调佣方式
      adjustCommissionWayCode,
    } = this.props;
    const {
      custIDList,
      popoverDisplay,
      selectedRows,
      pageNum,
      searchList,
      currentShowCustList,
      batchId,
      processData,
      uploadPopoverVisible,
    } = this.state;

    const {
      newCommission,
    } = otherPayload;

    // 客户列表分页
    const paginationOption = {
      current: pageNum,
      total: custIDList.length,
      pageSize: DEFAULT_PAGE_SIZE,
      onChange: this.handleCustPageChange,
      isHideLastButton: true,
      selectedNumber: selectedRows.length,
    };
    const rowSelection = {
      selectedRowKeys: _.map(selectedRows, 'custId'),
      hideDefaultSelections: true,
      columnWidth: 40,
      onSelect: this.handleSelectChange,
      onSelectAll: this.handleSelectAll,
      onSelectInvert: this.handleSelectAll,
    };
    // 是否是线上调佣(线上调佣需要展示对应的提示)
    const isOnline = adjustCommissionWayCode === 'ONLINE';

    return (
      <div className={styles.addCustomersBox}>
        <div className={styles.operationOfCustList}>
          <div className={styles.searchSelectArea}>
            客户：
            <AutoComplete
              placeholder="经纪客户号/客户名称"
              showNameKey="custName"
              showIdKey="brokerNumber"
              optionList={searchList}
              onSelect={this.handleSelectCustomer}
              onSearch={this.handleSearchCustomer}
              ref={this.queryCustomerComponentRef}
              dropdownMatchSelectWidth={false}
              getPopupContainer={getPopupContainerFunction}
              style={{ width: '200px' }}
            />
            <Button
              ghost
              disabled={disabled}
              type="primary"
              onClick={this.handleAddCustomer}
            >
              添加
            </Button>
            <Tooltip title="当前功能不支持选取当日新开客户" placement="top">
              <div className={styles.custInfoTip}>
                <AntdIcon type="exclamation-circle" />
              </div>
            </Tooltip>
          </div>
          <div className={styles.delectCustomerButton}>
            <IfWrap when={!_.isEmpty(newCommission) && !disabled}>
              <BusinessExcelUploader
                hasData={!_.isEmpty(custIDList)}
                file={CommissionXLS}
                limit={LIMIT_COUNT}
                payloadKey="custs"
                format={[['custId', 'custName']]}
                otherPayload={otherPayload}
                validator={this.handleMultiValidator}
                validateData={batchId}
                queryProgress={this.handleQueryValidProcess}
                progress={processData}
                callback={this.handleValidateFinish}
                popoverVisible={uploadPopoverVisible}
                beforeUpload={this.handleBeforeUpload}
                // showValidateModal
                // renderValidateModal={this.renderValidateModal}
              />
            </IfWrap>
            <Button disabled={disabled} onClick={this.handleDeleteCustomer}>
              移除
            </Button>
          </div>
        </div>
        <div className={styles.tableDiv}>
          <div className={styles.divPopover} style={{ display: popoverDisplay }}>
            <div className={styles.arrow} />
            勾选仅为当前页，请翻页再次勾选
          </div>
          <Table
            dataSource={currentShowCustList}
            columns={CUSTOMER_COLUMNS}
            rowKey="custId"
            rowSelection={rowSelection}
            pagination={false}
            placeHolderImageProps={CUSTOMER_PLACEHOLDER_PROPS}
            withBorder={_.isEmpty(currentShowCustList)}
            useNewUI
          />
          <IfWrap when={isOnline}>
            <div className={styles.blockTip}>
              <div className={styles.icon}><Icon type="tishi2" /></div>
              <div className={styles.text}>{ONLINE_TIP}</div>
            </div>
          </IfWrap>
          <Pagination {...paginationOption} />
        </div>
      </div>
    );
  }
}
