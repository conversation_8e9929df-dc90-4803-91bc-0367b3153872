/* eslint-disable max-len */
/**
 * @file components/commissionAdjustment/AuthorizeCommissionDetail.js
 *  佣金授权详情组件，参数：detailData- 基础详情数据,authCommissionCustList-客户列表数据，historyFlow - 审批历史数据
 * <AUTHOR>
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-08-22 17:00:00
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import { Popover, Radio } from 'antd';
import { connect } from 'dva';
import { dva } from '@/helper';
import { sensors } from '@lego/bigbox-utils';
import {
  DetailBlock,
  InfoCell,
  InfoGroup,
} from '@crm/biz-ui';
import Upload from '@/newUI/upload';
import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import IFWrap from '@/components/common/IFWrap';
import ApprovalHistory from '@/components/common/ApprovalHistory';
import { viewFilePdf } from '@/components/commissionAuthorization/utils';
import { format as formatTime } from '@/helper/time';
import {
  NORMAL_COMMISSION_RATES,
  CREDIT_COMMISSION_RATES,
  CUSTOM_MINIMUM_COMMISSION_RATES,
  MINIMUM_COMMISSION_RATES,
  CHANNEL_COMMISSION_CODE,
  SENSORS_MODIFY,
  OCOMMISSION_PARAM_CODE,
  OTHER_CODE
} from '@/components/commissionAuthorization/config';
import FileInfo from './FileInfo';
import warningIcon from './svg/warning.svg';
import styles from './authorizeCommissionDetail.less';

import {
  AUTHORIZE_CHANNEL_COMMISSION_COLUMNS,
  AUTHORIZE_ONLINE_COMMISSION_COLUMNS,
  SELECT,
  NO_SELECT
} from './config';

const DEFAULT_VALUE = '--';
const RESULT_SUCC = 'suc';
const RESULT_FAIL = 'fail';
const RESULT_OTHER = 'other';

const { logCommon } = sensors;
const RadioGroup = Radio.Group;
const effect = dva.generateEffect;

const mapStateToProps = (state) => ({
  // 佣金授权申请-详情信息
  detailData: state.commission.authCommissionDetail,
  // 佣金授权申请-客户列表
  authCommissionCustList: state.commission.authCommissionCustList,
});

const mapDispatchToProps = {
  // 佣金授权申请-查询详情客户列表
  queryCustInfoDetail: effect('commission/queryCustInfoDetail', { loading: true }),
};

@connect(mapStateToProps, mapDispatchToProps)
export default class AuthorizeCommissionDetail extends PureComponent {
  static propTypes = {
    detailData: PropTypes.object.isRequired,
    // 查询客户列表函数
    queryCustInfoDetail: PropTypes.func.isRequired,
    // 客户列表数据
    authCommissionCustList: PropTypes.object.isRequired,
  }

  componentDidUpdate(prevProps) {
    const { detailData } = this.props;
    const { detailData: prevDetailData } = prevProps;
    if (detailData?.appId !== prevDetailData?.appId) {
      this.getCustInfoDetail();
    }
  }

  // 查询详情中客户列表
  @autobind
  getCustInfoDetail(parmas = {}) {
    const { detailData } = this.props;
    this.props.queryCustInfoDetail({
      pageSize: 10,
      pageNum: 1,
      appId: detailData?.appId,
      recordId: detailData?.recordId,
      scenario: SENSORS_MODIFY,
      ...parmas,
    });
  }

  @autobind
  handleTransText(value) {
    return _.isEmpty(value) ? '--' : value;
  }

  @autobind
  renderConfirmResultColumn(item) {
    return {
      ...item,
      render: (text) => {
        const transText = this.handleTransText(text);
        const textStyle = transText === '确认失败' ? { color: '#ff0000' } : {};
        return (<span style={textStyle}>{transText}</span>);
      }
    };
  }

  @autobind
  renderCommissionStatus(text = '', record) {
    // 调佣失败字体颜色不同
    const showRedText = text === '调佣失败' || text === '部分失败';
    const transText = _.isEmpty(text) ? DEFAULT_VALUE : text;
    // // 调佣失败鼠标悬浮展示失败理由，否则不展示
    if (showRedText) {
      return (
        <span style={{ color: '#de350b' }}>
          {transText}
          <ToolTipCell
            placement="top"
            cellText={<img src={warningIcon} className={styles.statusWarningIcon} />}
            tipContent={record?.adjustReason}
          />
        </span>
      );
    }
    return (
      <span>{transText}</span>
    );
  }

  @autobind
  renderFileColumn(text = '', record) {
    const { fileInfo } = record;
    if (_.isEmpty(fileInfo)) {
      return '--';
    }
    // 附件只有一个，所以取第一个就行
    const attachInfo = fileInfo[0] || {};
    const PopContent = (
      <FileInfo fileInfo={attachInfo} />
    );
    return (
      <Popover
        placement="topLeft"
        content={PopContent}
        trigger="click"
        overlayClassName={styles.popover}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
      >
        <div className={styles.fileName}>
          {attachInfo?.name}
        </div>
      </Popover>
    );
  }

  @autobind
  renderColumns(columns) {
    return _.map(columns, (item) => {
      // 调佣状态
      if (item.key === 'adjustStatusName') {
        return {
          ...item,
          title: <div className={styles.statusName}>调佣状态</div>,
          render: (text, record) => this.renderCommissionStatus(text, record),
        };
      }
      // 附件
      if (item.key === 'confirmContract') {
        return {
          ...item,
          render: (text, record) => this.renderFileColumn(text, record),
        };
      }
      // 客户确认结果列
      if (item.key === 'custConfirmStatusName') {
        return this.renderConfirmResultColumn(item);
      }
      return {
        ...item,
        render: (text) => (
          <ToolTipCell
            cellText={text || DEFAULT_VALUE}
            tipContent={text || DEFAULT_VALUE}
          />
        )
      };
    });
  }

  @autobind
  filterOnLineColumns(list, isOnline) {
    let newList = list;
    const { detailData } = this.props;
    if (detailData?.typeCode !== OTHER_CODE) {
      newList = _.filter(list,
        (item) => !_.includes(['unifiedCommRaiseFlag'], item?.key));
    }
    if (isOnline) {
      return newList;
    }
    return _.filter(newList, (item) => !_.includes(['custConfirmStatusName', 'confirmContract'], item?.key));
  }

  @autobind
  getColumns() {
    const { detailData } = this.props;
    const isChannelCommission = detailData?.typeCode === CHANNEL_COMMISSION_CODE; // 是否渠道调佣
    const isOnline = detailData?.adjustCommissionWayCode === 'ONLINE'; // 是否是线上调佣
    const columns = isChannelCommission
      ? this.filterOnLineColumns(AUTHORIZE_CHANNEL_COMMISSION_COLUMNS, isOnline) : this.filterOnLineColumns(AUTHORIZE_ONLINE_COMMISSION_COLUMNS, isOnline);

    return this.renderColumns(columns);
  }

  // 客户翻页事件
  @autobind
  handleCustPageChange(pageNum) {
    logCommon({
      type: 'click',
      payload: {
        name: '佣金授权申请详情-客户列表-切换分页',
        value: pageNum,
      },
    });
    this.getCustInfoDetail({ pageNum });
  }

  @autobind
  renderTextCell(value) {
    return _.isEmpty(value) ? '--' : value;
  }

  @autobind
  validateCustStatus(custList) {
    if (_.isEmpty(custList)) {
      return false;
    }

    const showFailTip = _.some(custList, (item) => item?.adjustStatusName?.indexOf ('失败') > -1);
    const showSucTip = _.some(custList, (item) => item?.adjustStatusName === '模板已匹配');

    // 存在失败
    if (showFailTip) {
      return RESULT_FAIL;
    }

    if (showSucTip) {
      return RESULT_SUCC;
    }

    return RESULT_OTHER;
  }

  @autobind
  renderCommissionRate(list, twoColumns = false) {
    const { detailData } = this.props;
    return _.map(list, (item, index) => {
      const { brief, paramName } = item;
      const span = twoColumns && (_.includes(paramName, 'credit') || _.includes(paramName, 'Credit')) ? 66 : 33;
      const content = detailData?.[`${paramName}Name`]
        || '不变';
      return (
        <InfoCell
          span={span}
          label={brief}
          content={content}
          key={index}
          required={paramName === OCOMMISSION_PARAM_CODE}
        />
      );
    });
  }

  @autobind
  handleViewFile(file, label) {
    logCommon({
      type: 'Click',
      payload: {
        name: `佣金授权申请详情-附件-预览${label}`,
        value: JSON.stringify(file)
      },
    });
    return viewFilePdf(file);
  }

  @autobind
  handleDownLoad(label) {
    logCommon({
      type: 'Click',
      payload: {
        name: `佣金授权申请详情-附件-下载${label}`,
      },
    });
  }

  @autobind
  getMultiMiniChargeRates(detailData) {
    return detailData?.multiMiniChargeSet === SELECT
      ? CUSTOM_MINIMUM_COMMISSION_RATES : MINIMUM_COMMISSION_RATES;
  }

  render() {
    const { detailData, authCommissionCustList } = this.props;

    const {
      contentAttachment = [],
      otherAttachment = [],
      typeName,
      adjustCommissionWayName,
      custTypeName,
      projectName,
      description,
      multiMiniChargeSet = '',
      createDate = '',
    } = detailData || {};

    if (_.isEmpty(detailData)) {
      return null;
    }

    // 客户列表分页信息
    const paginationOption = {
      current: authCommissionCustList?.curPageNum || 1,
      total: authCommissionCustList?.totalCount || 0,
      pageSize: authCommissionCustList?.pageSize || 10,
      onChange: this.handleCustPageChange,
    };

    // 1、基础数据解析
    const bugTitle = `编号:${detailData?.appId || DEFAULT_VALUE}`;
    const creator = detailData?.creatorOrgName ? `${detailData?.creatorOrgName}-${detailData?.creatorName}(${detailData?.creatorId})` : '--'; // 拟稿人
    // 申请时间
    const applyTime = createDate ? formatTime(createDate) : DEFAULT_VALUE;
    // 2、客户列表
    const finalColumns = this.getColumns();
    const allWidth = _.sum(_.map(finalColumns, (item) => item.width));
    // 是否展示提示语：调佣成功优化，有客户调佣状态为【模板已匹配】则展示
    const showTip = this.validateCustStatus(authCommissionCustList?.custInfos);
    const multiMiniChargeRates = this.getMultiMiniChargeRates(detailData);
    // 是否渠道调佣
    const isChannelCommission = detailData?.typeCode === CHANNEL_COMMISSION_CODE;

    return (
      <div className={styles.detailBox}>
        <h1 className={styles.title}>
          {bugTitle}
          <IFWrap when={showTip === RESULT_FAIL}>
            <span className={styles.tipText}>
              <img src={warningIcon} className={styles.warningIcon} />
              存在调佣失败情况，请查看失败原因。请及时跟踪客户首笔交易。
            </span>
          </IFWrap>
          <IFWrap when={showTip === RESULT_SUCC}>
            <span className={styles.tipText}>
              <img src={warningIcon} className={styles.warningIcon} />
              调佣设置已完成，佣金模版已匹配，请跟踪客户首笔交易。
            </span>
          </IFWrap>
        </h1>
        <DetailBlock title="基本信息">
          <InfoGroup labelWidth="190px">
            <InfoCell span={33} label="申请类型" content={typeName || DEFAULT_VALUE} />
            <InfoCell span={33} label="调佣方式" content={adjustCommissionWayName || DEFAULT_VALUE} />
            <InfoCell span={33} label="客户性质" content={custTypeName || DEFAULT_VALUE} />
            <InfoCell span={100} label="项目名称" content={projectName || DEFAULT_VALUE} />
            <InfoCell span={100} label="项目说明" content={<div className={styles.description}>{description || DEFAULT_VALUE}</div>} />
          </InfoGroup>
        </DetailBlock>
        <DetailBlock title="拟稿信息">
          <InfoGroup labelWidth="190px">
            <InfoCell span={100} label="拟稿人" content={creator} />
            <InfoCell span={100} label="申请时间" content={applyTime} />
          </InfoGroup>
        </DetailBlock>
        <DetailBlock title="客户列表">
          <div className={styles.custListWrap}>
            <Table
              dataSource={authCommissionCustList?.custInfos || []}
              columns={finalColumns}
              pagination={false}
              rowKey="custId"
              useNewUI
              spaceColumnProps={{ width: isChannelCommission ? '50px' : '59px' }}
              scroll={{ x: allWidth }}
            />
            <Pagination {...paginationOption} />
          </div>
        </DetailBlock>
        <DetailBlock title="佣金费率设置">
          <div className={styles.commissionType}>普通账户</div>
          <InfoGroup labelWidth="190px">
            {this.renderCommissionRate(NORMAL_COMMISSION_RATES)}
          </InfoGroup>
          <div className={styles.commissionType}>信用账户</div>
          <InfoGroup labelWidth="190px">
            {this.renderCommissionRate(CREDIT_COMMISSION_RATES)}
          </InfoGroup>
        </DetailBlock>
        <DetailBlock title="账户最低收费设置">
          <InfoGroup labelWidth="190px">
            <InfoCell
              span={100}
              label="是否定制化设置最低收费"
              content={(
                <RadioGroup
                  value={multiMiniChargeSet}
                  size="normal"
                  disabled
                >
                  <Radio key={SELECT} value={SELECT}>是</Radio>
                  <Radio key={NO_SELECT} value={NO_SELECT}>否</Radio>
                </RadioGroup>
              )}
            />
            {this.renderCommissionRate(multiMiniChargeRates, true)}
          </InfoGroup>
        </DetailBlock>
        <DetailBlock title="附件">
          <div className={styles.fileWrap}>
            <InfoCell
              span={100}
              label="正文附件"
              content={(
                <Upload
                  key="contentAttachmentUploader"
                  disabled
                  removeable={false}
                  defaultFileList={contentAttachment}
                  previewable
                  onViewFile={(file) => this.handleViewFile(file, '正文附件')}
                  onDownload={() => this.handleDownLoad('正文附件')}
                />
              )}
            />
            <InfoCell
              span={100}
              label="其他附件"
              content={(
                <Upload
                  key="contentAttachmentUploader"
                  disabled
                  removeable={false}
                  defaultFileList={otherAttachment}
                  previewable
                  onViewFile={(file) => this.handleViewFile(file, '其他附件')}
                  onDownload={() => this.handleDownLoad('其他附件')}
                />
              )}
            />
          </div>
        </DetailBlock>
        <DetailBlock title="审批记录">
          <ApprovalHistory
            data={detailData?.flowHistory || []}
          />
        </DetailBlock>
      </div>
    );
  }
}
