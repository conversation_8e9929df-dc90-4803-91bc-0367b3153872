/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: Description
 * @Date: 2019-04-04 16:10:23
 * @Last Modified by: sunweibin
 * @Last Modified time: 2022-02-23 14:21:19
 */

export const DROP_DOWN_STYLE = {
  maxHeight: 380,
  overflowY: 'auto',
  width: 255
};

export const ALIGN_CONFIG = {
  points: ['br', 'tl']
};

export const TIME_FORMAT_STR = 'YYYY-MM-DD HH:mm';

// 渠道号的key值
export const CHANNEL_NUM_KEY = 'channelList';
// 场内基金费率的key值
export const INSIDE_FUNDS_RATE_KEY = 'insideFundsRateCode';
// 普通股基费率的key值
export const NORMAL_STOCK_RATE_KEY = 'normalStockRateCode';
// 债券佣金费率的key值
export const BOND_COMMISSION_RATE_KEY = 'bondCommRateCode';
// 回购费率的key值
export const BUY_BACK_RATE_KEY = 'repcCommRateCode';
// 目标产品的key值
export const TARGET_PRODUCT_KEY = 'targetProductCode';
// 审批人的key值
export const APPROVER_KEY = 'approverId';

// 字典接口中债券佣金费率下拉选项的key值
export const BOND_COMMISSION_OPTIONS_KEY = 'HTSC_ZFARE_RATIO';

// 字典接口中回购费率下拉选项的key值
export const BUY_BACK_OPTIONS_KEY = 'HTSC_HFARE_RATIO';

// 备注的key
export const REAMRK_KEY = 'remark';

// 审批意见的key
export const OPINION_KEY = 'approvalAdvices';

// 字典对象里 dict.otherRatio 中场内基金费用类型 的key
export const DICT_INSIDE_FUNDS_RATE_KEY = 'HTSC_OFARE_RATIO';

// 账户类别普通账户的code
export const ACCOUNT_NORMAL_TYPE_CODE = '01';

// 账户类别的map
export const ACCOUNT_TYPE_MAP = {
  [ACCOUNT_NORMAL_TYPE_CODE]: '普通账户'
};

export const TITLE_LIST = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '经纪客户号',
    width: 102,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 77,
  },
  {
    dataIndex: 'custLevelName',
    key: 'custLevelName',
    title: '客户等级',
    width: 126,
  },
  {
    dataIndex: 'departmentName',
    key: 'departmentName',
    title: '开户营业部',
    width: 140,
  },
  {
    dataIndex: 'empId',
    key: 'empId',
    title: '服务经理',
    width: 120,
  },
  {
    dataIndex: 'triggered',
    key: 'triggered',
    title: '触发方式',
    width: 84,
  },
  {
    dataIndex: 'triggereTime',
    key: 'triggereTime',
    title: '触发时间',
    width: 119,
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '状态',
    width: 36,
  },
  {
    dataIndex: 'updateTime',
    key: 'updateTime',
    title: '更新时间',
    width: 119,
  }
];

// 自动外汇调佣申请详情中的【渠道信息】表格 Columns 配置
export const CHANNELS_COLUMNS = [
  {
    dataIndex: 'channelNumber',
    key: 'channelNumber',
    title: '渠道编号',
    width: 203,
  },
  {
    dataIndex: 'channelName',
    key: 'channelName',
    title: '渠道名称',
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '渠道状态',
    width: 151,
  },
  {
    dataIndex: 'updatedTime',
    key: 'updatedTime',
    title: '终止时间',
    width: 102,
  },
];
// 表格空白间隙配置
export const TABLE_SPACE_PROPS = { width: '20px' };
// 自动外呼调佣申请详情中的渠道列表信息的【状态】列需要根据后端返回的值展示对应的中文
export const ChannelStatusTextMapping = {
  IN_FLOW_PROGRESS: '处理中',
  APPROVE_OVER: '外呼调佣中',
  REJECT: '已终止',
};

// 自动外呼调佣申请中 未处理的code
export const UN_TREATED_CODE = 'NOT_HANDLER';
// 自动外呼调佣申请中 处理中的code
export const PEDDING_CODE = 'PROCESSING';
// 自动外呼调佣申请中 完成的code
export const SUCCESS_CODE = 'SUCCESS';
// 自动外呼调佣申请中 已生成订单的失败的code
export const FILED_CODE = 'FAILURE';

export const STATUS_MAP = {
  [UN_TREATED_CODE]: '未处理',
  [PEDDING_CODE]: '处理中',
  [SUCCESS_CODE]: '已完成',
  [FILED_CODE]: '已失败'
};

export const CUSTOMER_PLACEHOLDER_PROPS = {
  title: '暂无客户数据'
};

// 表格的表头
export const CUSTOMER_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '经纪客户号'
  },
  {
    title: '客户名称',
    dataIndex: 'custName',
    key: 'custName'
  },
  {
    title: '客户等级',
    dataIndex: 'levelName',
    key: 'levelName'
  },
  {
    title: '开户营业部',
    dataIndex: 'openOrgName',
    key: 'openOrgName'
  }
];
export const DEFAULT_PAGE_SIZE = 5;

export const TEXT_Y = 'Y';
export const HAS_CHECK = 'hasCheck';
export const HAS_REJECTED = 'hasRejected';
export const HAS_NEW = 'hasNew';
export const LIMIT_COUNT = 1000;

// 旧的批量佣金数据的columns
export const OLD_BATCH_COMMISSION_COLUMNS = [
  {
    dataIndex: 'econNum',
    key: 'econNum',
    title: '经纪客户号',
    width: 110,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 90,
  },
  {
    dataIndex: 'custLevel',
    key: 'custLevel',
    title: '客户等级',
    width: 96,
  },
  {
    dataIndex: 'openAccDept',
    key: 'openAccDept',
    title: '开户营业部',
    width: 210,
  },
  {
    dataIndex: 'adjustCommissionWayStatus',
    key: 'adjustCommissionWayStatus',
    title: '调佣状态',
    width: 150,
  },
  {
    dataIndex: 'view',
    key: 'view',
    title: '审批记录',
    width: 80,
  },
];

// 旧的批量佣金数据的columns
export const NEW_BATCH_COMMISSION_COLUMNS = [
  {
    dataIndex: 'econNum',
    key: 'econNum',
    title: '经纪客户号',
    width: 110,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 90,
  },
  {
    dataIndex: 'custLevel',
    key: 'custLevel',
    title: '客户等级',
    width: 96,
  },
  {
    dataIndex: 'openAccDept',
    key: 'openAccDept',
    title: '开户营业部',
    width: 210,
  },
  {
    dataIndex: 'adjustCommissionWayStatus',
    key: 'adjustCommissionWayStatus',
    title: '调佣状态',
    width: 150,
  },
  {
    dataIndex: 'approveFlag',
    key: 'approveFlag',
    title: '审批结果',
    width: 80,
  },
];
// 批量佣金调整（智能外呼）新增的列
export const SMARTCALL_COLUMN = [
  {
    dataIndex: 'exhalationTime',
    key: 'exhalationTime',
    title: '呼出时间',
    width: 120,
  },
  {
    dataIndex: 'callOutStatus',
    key: 'callOutStatus',
    title: '呼出状态',
    width: 80,
  },
  {
    dataIndex: 'exhaledResults',
    key: 'exhaledResults',
    title: '呼出结果',
    width: 80,
  },
];
// 批量佣金调整（智能外呼）呼出结果-详情弹窗columns
export const SMARTCALL_RESULT_COLUMN = [
  {
    dataIndex: 'questionId',
    key: 'questionId',
    title: '问题ID',
    width: 50,
  },
  {
    dataIndex: 'question',
    key: 'question',
    title: '问题',
    width: 200,
  },
  {
    dataIndex: 'answer',
    key: 'answer',
    title: '回答内容',
    width: 180,
  },
];
// 场内基金费率万三的节点，小于此值的即为小于万三的
// 2025-02-19 新增场内基金模板，场内基金万三的code由8730改为7370
export const WAN_FEN_ZHI_SAN_CODE = '7370';
export const ZERO_DOT_THREE = 0.3;

// 单佣金调整时用户可以选择是否调整当前股基佣金率
export const ADJUST = 'adjust';
export const NOT_ADJUST = 'notAdjust';
export const ADJUST_TYPE_LIST = [
  {
    key: ADJUST,
    value: '调整',
  },
  {
    key: NOT_ADJUST,
    value: '不调整',
  },
];
// 批量调佣智能外呼执行方式
export const EXECUTION_STYLE = [
  {
    key: 'smartCall',
    value: '是',
  },
  {
    key: 'unlimited',
    value: '否'
  }
];
// 单佣金调整-其它佣金率-普通账户收取最低费用
export const NORMAL_KEY = 'HTSC_NORMAL_RATIO';
// 单佣金调整-其它佣金率-信用账户收取最低费用
export const CREDIT_KEY = 'HTSC_CREDIT_RATIO';
// 其他佣金率-B股
export const B_GU_KEY = 'HTSC_BGFARE_RATIO';

// 特殊佣金调整详情下客户表格Columns
export const SPECIAL_COMMISSION_TABLE_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '经纪客户号',
    width: 102,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 77,
  },
  {
    dataIndex: 'riskLevelText',
    key: 'riskLevelText',
    title: '风险等级',
    width: 56,
  },
  {
    dataIndex: 'serviceOrgName',
    key: 'serviceOrgName',
    title: '服务营业部',
    width: 140,
  },
  {
    dataIndex: 'custLevelText',
    key: 'custLevelText',
    title: '客户等级',
    width: 126,
  },
  {
    dataIndex: 'openOrgName',
    key: 'openOrgName',
    title: '开户营业部',
    width: 140,
  },
  {
    dataIndex: 'empId',
    key: 'empId',
    title: '服务经理',
    width: 115,
  },
  {
    dataIndex: 'totalAsset',
    key: 'totalAsset',
    title: '总资产(元)',
    width: 121,
    align: 'right',
  },
  {
    dataIndex: 'yearStockExchange',
    key: 'yearStockExchange',
    title: '近一年股基交易量(元)',
    width: 137,
    align: 'right',
  },
  {
    dataIndex: 'yearStockProfit',
    key: 'yearStockProfit',
    title: '近一年股基毛佣金(元)',
    width: 137,
    align: 'right',
  },
];
// 特殊佣金调整-审批人列表配置项
export const APPROVAL_COLUMNS = [
  {
    title: '工号',
    dataIndex: 'empId',
    key: 'empId',
  }, {
    title: '姓名',
    dataIndex: 'empName',
    key: 'empName',
  }, {
    title: '所属营业部',
    dataIndex: 'orgName',
    key: 'orgName',
  },
];
// 特殊佣金调整-默认值
export const DEFAULT_VALUE = '--';
// 特殊佣金调整-发起流程名称
export const FLOW_BPMNAME = {
  per: '特殊佣金调整审批流程-普通个人户',
  org: '特殊佣金调整审批流程-机构产品户',
  prod: '特殊佣金调整审批流程-机构产品户',
};
// 客户信息-表格间距
export const SPACE_20 = {
  width: 20,
};

// 客户筛选组建下拉选项样式
export const CUST_DROPDOWN_STYLE = {
  maxHeight: 324,
  overflowY: 'auto',
  width: 250,
};

// 场内基金下拉选项-ETL申赎code值
export const ETL_REDEMPTION_CODE = [
  '8670', // 交易万1.0_ETF申赎0
  '8671', // 交易万1.0_ETF申赎万0.5
  '8672', // 交易万1.0_ETF申赎万1.0
  '8673', // 交易万1.1_ETF申赎0
  '8674', // 交易万1.1_ETF申赎万0.5
  '8675', // 交易万1.1_ETF申赎万1.0
  '8676', // 交易万1.2_ETF申赎0
  '8677', // 交易万1.2_ETF申赎万0.5
  '8678', // 交易万1.2_ETF申赎万1.0
  '8679', // 交易万1.3_ETF申赎0
  '8680', // 交易万1.3_ETF申赎万0.5
  '8681', // 交易万1.3_ETF申赎万1.0
  '8682', // 交易万1.4_ETF申赎0
  '8683', // 交易万1.4_ETF申赎万0.5
  '8684', // 交易万1.4_ETF申赎万1.0
  '8685', // 交易万1.5_ETF申赎0
  '8686', // 交易万1.5_ETF申赎万0.5
  '8687', // 交易万1.5_ETF申赎万1.0
  '8688', // 交易万2.0_ETF申赎0
  '8689', // 交易万2.0_ETF申赎万0.5
  '8690', // 交易万2.0_ETF申赎万1.0
];

// 客户确认结果列
export const CUST_CONFIRM_RESULT_COLUMN = {
  dataIndex: 'custConfirmResult',
  key: 'custConfirmResult',
  title: '客户确认结果',
  width: 84,
};

// 批量佣金调整（线上调佣）新增的列
export const ADJUST_COMMISSION_COLUMN = [
  CUST_CONFIRM_RESULT_COLUMN,
  {
    dataIndex: 'file',
    key: 'file',
    title: '附件',
    width: 80,
  },
];
// 单佣金调整时用户可以选择OA重要事项特殊佣金申请
export const SELECT = 'Y';
export const NO_SELECT = 'N';
// 非标客户特殊佣金调整详情下客户表格Columns
export const UN_STANDARD_CUST_TABLE_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '经纪客户号',
    width: 102,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 77,
  },
  {
    dataIndex: 'futureStandardTime',
    key: 'futureStandardTime',
    title: '客户承诺未来达标时间',
    width: 140,
  },
  {
    dataIndex: 'standardTime',
    key: 'standardTime',
    title: '承诺达标日期',
    width: 84,
  },
  {
    dataIndex: 'standardFlag',
    key: 'standardFlag',
    title: '是否达标',
    width: 56,
  },
  {
    dataIndex: 'timeTotalAsset',
    key: 'timeTotalAsset',
    title: 'T-1日总资产(元)',
    width: 101,
  },
  {
    dataIndex: 'sumGjAmt',
    key: 'sumGjAmt',
    title: '承诺期累计股基交易量(元)',
    width: 165,
  },
  {
    dataIndex: 'serviceOrgName',
    key: 'serviceOrgName',
    title: '服务营业部',
    width: 140,
  },
  {
    dataIndex: 'empId',
    key: 'empId',
    title: '服务经理',
    width: 115,
  },
  {
    dataIndex: 'openOrgName',
    key: 'openOrgName',
    title: '开户营业部',
    width: 140,
  },
];

// 佣金授权申请-渠道调佣客户列表的columns
export const AUTHORIZE_CHANNEL_COMMISSION_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '客户号',
    width: 110,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 90,
  },
  {
    dataIndex: 'channelName',
    key: 'channelName',
    title: '渠道标识',
    width: 120,
  },
  {
    dataIndex: 'activeTime',
    key: 'activeTime',
    title: '激活时间',
    width: 120,
  },
  {
    dataIndex: 'serviceOrgName',
    key: 'serviceOrgName',
    title: '服务营业部',
    width: 110,
  },
  {
    dataIndex: 'openOrgName',
    key: 'openOrgName',
    title: '开户营业部',
    width: 110,
  },
  {
    dataIndex: 'zlPickInvestFlag',
    key: 'zlPickInvestFlag',
    title: '是否涨乐选投顾',
    width: 98,
  },
  {
    dataIndex: 'zlPickInvestDate',
    key: 'zlPickInvestDate',
    title: '最近一次涨乐选投顾时间',
    width: 149
  },
  {
    dataIndex: 'adjustStatusName',
    key: 'adjustStatusName',
    title: '调佣状态',
    width: 70,
  },
  {
    dataIndex: 'custConfirmStatusName',
    key: 'custConfirmStatusName',
    title: '客户确认结果',
    width: 80,
  },
  {
    dataIndex: 'confirmContract',
    key: 'confirmContract',
    title: '附件',
    width: 110,
  },
];
// 佣金授权申请-线上调佣客户列表的columns
export const AUTHORIZE_ONLINE_COMMISSION_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '客户号',
    width: 110,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 90,
  },
  {
    dataIndex: 'serviceOrgName',
    key: 'serviceOrgName',
    title: '服务营业部',
    width: 110,
  },
  {
    dataIndex: 'openOrgName',
    key: 'openOrgName',
    title: '开户营业部',
    width: 110,
  },
  {
    dataIndex: 'unifiedCommRaiseFlag',
    key: 'unifiedCommRaiseFlag',
    title: '是否账户升佣客户',
    width: 98,
  },
  {
    dataIndex: 'zlPickInvestFlag',
    key: 'zlPickInvestFlag',
    title: '是否涨乐选投顾',
    width: 98,
  },
  {
    dataIndex: 'zlPickInvestDate',
    key: 'zlPickInvestDate',
    title: '最近一次涨乐选投顾时间',
    width: 149,
  },
  {
    dataIndex: 'adjustStatusName',
    key: 'adjustStatusName',
    title: '调佣状态',
    width: 70,
  },
  {
    dataIndex: 'custConfirmStatusName',
    key: 'custConfirmStatusName',
    title: '客户确认结果',
    width: 80,
  },
  {
    dataIndex: 'confirmContract',
    key: 'confirmContract',
    title: '附件',
    width: 110,
  },
];

// 佣金授权申请-审批历史记录columns
export const AUTHORIZE_APPROVAL_HISTORY_COLUMNS = [
  {
    dataIndex: 'cIndex',
    key: 'cIndex',
    title: '序号',
    width: 80,
  },
  {
    dataIndex: 'person',
    key: 'person',
    title: '处理人',
    width: 110,
  },
  {
    dataIndex: 'orgName',
    key: 'orgName',
    title: '部门',
    width: 140,
  },
  {
    dataIndex: 'stepName',
    key: 'stepName',
    title: '审批节点',
    width: 120,
  },
  {
    dataIndex: 'approvalResult',
    key: 'approvalResult',
    title: '审批结果',
    width: 110,
  },
  {
    dataIndex: 'approvalAdvices',
    key: 'approvalAdvices',
    title: '审批意见',
    width: 110,
  },
  {
    dataIndex: 'endTime',
    key: 'endTime',
    title: '审批时间',
    width: 110,
  },
];

// 客户承诺未来达标时间-30日
export const FUTURE_TIME_30 = '01';

// 需要展示是否设置多档最低收费佣金的客户类型集合
export const SHOW_RADIO_CUSTS = ['org', 'prod', 'per'];
