/*
 * @Author: yanfaping
 * @Date: 2024-08-28 17:40:57
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-01-08 09:40:19
 * @Description: 佣金授权申请-导入客户列表
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import { connect } from 'dva';
import _ from 'lodash';
import { Popconfirm } from 'antd';
import { dva } from '@/helper';
import { sensors } from '@lego/bigbox-utils';

import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import {
  CHANNEL_COMMISSION_CODE,
  SENSORS_NEW,
  SENSORS_EXISTED,
  OTHER_CODE
} from '@/components/commissionAuthorization/config';
import EmptyDataIcon from '@/components/commissionAuthorization/images/emptyData.svg';
import { CUSTOMER_COLUMNS, DEFAULT_PAGE_SIZE, DEFAULT_VALUE } from './config';
import BusinessExcelUploader from './BusinessExcelUploader';

import styles from './index.less';

const { logCommon } = sensors;
const effect = dva.generateEffect;

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {
  // 文件解析接口
  parseExcel: effect('commissionAuthorization/parseExcel', { loading: false }),
  // 文件解析进度
  getValidProgress: effect('commissionAuthorization/getValidProgress', { loading: false }),
  // 文件解析结果
  queryValidResult: effect('commissionAuthorization/queryValidResult'),
  // 删除客户
  deleteImportCust: effect('commissionAuthorization/deleteImportCust'),
  // 查询附件
  getAttachmentList: effect('commissionAuthorization/getAttachmentList'),
};

@connect(mapStateToProps, mapDispatchToProps)
export default class AddCustomer extends PureComponent {
  static propTypes = {
    // 文件解析接口
    parseExcel: PropTypes.func.isRequired,
    // 文件解析进度
    getValidProgress: PropTypes.func.isRequired,
    // 文件解析结果
    queryValidResult: PropTypes.func.isRequired,
    // 删除客户
    deleteImportCust: PropTypes.func.isRequired,
    // 查询附件
    getAttachmentList: PropTypes.func.isRequired,
    // 设置导入成功客户信息
    onSetImportSuccessCust: PropTypes.func.isRequired,
    // 导入批次id
    batchId: PropTypes.string.isRequired,
    // 查询校验通过客户列表
    getCheckPassCustList: PropTypes.func.isRequired,
    // 客户列表数据
    custData: PropTypes.object.isRequired,
    // 导入按钮是否置灰
    disabled: PropTypes.bool,
    // 导入进度接口需要的参数
    importParams: PropTypes.object,
    // 神策日志
    logName: PropTypes.string,
    // 申请类型集合
    authCommissionType: PropTypes.array,
    // 申请类型
    typeCode: PropTypes.string,
  }

  static defaultProps = {
    disabled: false,
    importParams: {},
    logName: '服务订购-新建佣金授权申请',
    authCommissionType: [],
    typeCode: '',
  }

  @autobind
  handleDelete(record) {
    const { batchId, logName } = this.props;
    logCommon({
      type: 'click',
      payload: {
        name: `${logName}-客户列表-操作-删除-确定删除客户`,
      },
    });
    const type = !_.isEmpty(batchId) ? SENSORS_NEW : SENSORS_EXISTED;
    this.props.deleteImportCust({
      batchId,
      custId: record?.custId,
      custRecordId: record?.id,
      scenario: type,
      rowIndex: record?.rowIndex
    }).then((res) => {
      if (res) {
        this.props.getCheckPassCustList({
          pageSize: 10,
          pageNum: 1,
        });
      }
    });
  }

  @autobind
  handlePopconfirmCancel() {
    const { logName } = this.props;
    logCommon({
      type: 'click',
      payload: {
        name: `${logName}-客户列表-操作-删除-取消删除客户`,
      },
    });
  }

  @autobind
  handlePopupClick() {
    const { logName } = this.props;
    logCommon({
      type: 'click',
      payload: {
        name: `${logName}-客户列表-操作-删除`,
      },
    });
  }

  @autobind
  renderOperateColumn(column) {
    return {
      ...column,
      render: (text, record) => (
        <Popconfirm
          placement="topRight"
          title={(
            <div className={styles.popconfirmWrap}>
              <div className={styles.popconfirmTitle}>提示！</div>
              <div className={styles.popconfirmContent}>请确认是否删除当前客户</div>
            </div>
          )}
          okText="是"
          cancelText="否"
          getPopupContainer={(triggerNode) => triggerNode}
          onConfirm={() => this.handleDelete(record)}
          onCancel={this.handlePopconfirmCancel}
        >
          <span className={styles.deleteButton} onClick={this.handlePopupClick}>删除</span>
        </Popconfirm>
      ),
    };
  }

  @autobind
  renderTooltip(column) {
    return {
      ...column,
      render: (text) => {
        if (_.isNil(text) && _.isEmpty(text)) {
          return DEFAULT_VALUE;
        }
        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      },
    };
  }

  @autobind
  getCustomerColumns() {
    const { typeCode } = this.props;
    // 申请类型为【其他】-需要过滤掉渠道标识，激活时间
    let columns = [];
    if (typeCode === OTHER_CODE) {
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['channelName', 'activeTime'], item?.key));
    } else if (typeCode === CHANNEL_COMMISSION_CODE) {
      // 申请类型为【渠道】-需要过滤掉是否账户升佣客户
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['unifiedCommRaiseFlag'], item?.key));
    } else {
      // 申请类型为非【其他】【渠道】过滤掉渠道标识、激活时间、是否账户升佣客户
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['unifiedCommRaiseFlag', 'channelName', 'activeTime'], item?.key));
    }
    return _.map(columns, (column) => {
      const { key } = column;
      if (key === 'operate') {
        return this.renderOperateColumn(column);
      }
      return this.renderTooltip(column);
    });
  }

  @autobind
  handleCustPageChange(pageNum) {
    const { logName } = this.props;
    logCommon({
      type: 'click',
      payload: {
        name: `${logName}-客户列表-切换分页`,
        value: pageNum,
      },
    });
    this.props.getCheckPassCustList({
      pageNum: pageNum || 1,
      pageSize: 10,
    });
  }

  render() {
    const {
      parseExcel,
      getValidProgress,
      queryValidResult,
      disabled,
      importParams,
      custData,
      getAttachmentList,
      authCommissionType,
    } = this.props;
    // 客户列表分页
    const paginationOption = {
      current: custData?.curPageNum || 1,
      total: custData?.totalCount || 0,
      pageSize: custData?.pageSize || DEFAULT_PAGE_SIZE,
      onChange: this.handleCustPageChange,
    };

    return (
      <div className={styles.addCustomerWrap}>
        <BusinessExcelUploader
          getValidProgress={getValidProgress}
          parseExcel={parseExcel}
          callback={this.props.onSetImportSuccessCust}
          queryValidResult={queryValidResult}
          hasData={!_.isEmpty(custData?.custInfos)}
          disabled={disabled}
          importParams={importParams}
          getAttachmentList={getAttachmentList}
          authCommissionType={authCommissionType}
        />
        <Table
          className={styles.tableWrap}
          dataSource={custData?.custInfos || []}
          columns={this.getCustomerColumns()}
          rowKey="custId"
          pagination={false}
          placeHolderImageProps={{
            emptyImage: <img src={EmptyDataIcon} />
          }}
          useNewUI
        />
        <Pagination {...paginationOption} />
      </div>
    );
  }
}
