/*
 * @Author: yanfaping
 * @Date: 2024-08-28 17:40:57
 * @LastEditors: yanfaping
 * @LastEditTime: 2024-12-02 17:10:10
 * @Description: 佣金授权申请驳回修改-退反馈-客户列表
 */
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { sensors } from '@lego/bigbox-utils';

import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import { CUSTOMER_COLUMNS, DEFAULT_PAGE_SIZE, DEFAULT_VALUE } from '@/components/commissionAuthorization/AddCustomer/config';
import {
  CHANNEL_COMMISSION_CODE,
  OTHER_CODE
} from '@/components/commissionAuthorization/config';

import styles from './index.less';

const { logCommon } = sensors;

function CustList(props) {
  const {
    typeCode,
    custData,
  } = props;

  const columns = useMemo(() => {
    const readOnlyColumns = _.dropRight(CUSTOMER_COLUMNS);
    // 申请类型为【其他】-需要过滤掉渠道标识，激活时间
    let custColumns = [];
    if (typeCode === OTHER_CODE) {
      custColumns = _.filter(readOnlyColumns, (item) => !_.includes(['channelName', 'activeTime'], item?.key));
    } else if (typeCode === CHANNEL_COMMISSION_CODE) {
      // 申请类型为【渠道】-需要过滤掉是否账户升佣客户
      custColumns = _.filter(readOnlyColumns, (item) => !_.includes(['unifiedCommRaiseFlag'], item?.key));
    } else {
      // 申请类型为非【其他】【渠道】过滤掉渠道标识、激活时间、是否账户升佣客户
      custColumns = _.filter(readOnlyColumns, (item) => !_.includes(['unifiedCommRaiseFlag', 'channelName', 'activeTime'], item?.key));
    }
    return _.map(custColumns, (column) => ({
      ...column,
      render: (text) => {
        if (_.isNil(text) && _.isEmpty(text)) {
          return DEFAULT_VALUE;
        }
        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      },
    }));
  }, [CUSTOMER_COLUMNS, typeCode]);

  const handleCustPageChange = (pageNum) => {
    logCommon({
      type: 'click',
      payload: {
        name: '服务订购-佣金授权申请驳回修改-客户列表-切换分页',
        value: pageNum,
      },
    });
    props.queryApplyOrderCustList({ pageNum });
  };

  // 客户列表分页
  const paginationOption = {
    current: custData?.curPageNum || 1,
    total: custData?.totalCount || 0,
    pageSize: custData?.pageSize || DEFAULT_PAGE_SIZE,
    onChange: handleCustPageChange,
  };

  return (
    <div className={styles.customerListWrap}>
      <Table
        className={styles.tableWrap}
        dataSource={custData?.custInfos || []}
        columns={columns}
        rowKey="custId"
        pagination={false}
        useNewUI
      />
      <Pagination {...paginationOption} />
    </div>
  );
}

CustList.propTypes = {
  // 查询客户列表
  queryApplyOrderCustList: PropTypes.func.isRequired,
  // 客户列表
  custData: PropTypes.object.isRequired,
  // 申请类型
  typeCode: PropTypes.string,
};

CustList.defaultProps = {
  typeCode: '',
};

export default React.memo(CustList);
