/*
 * @Author: yanfaping
 * @Date: 2024-08-27 14:35:18
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-05-29 14:48:47
 * @Description: 佣金授权申请-相关页面配置项
 */
// 项目名称字数长度
export const NAME_MAX_LENGTH = 15;
// 项目说明字数长度
export const DESC_MAX_LENGTH = 200;
// 佣金授权场景key值
export const AUTHCOMMISSION_KEY = '0220';

// 调佣方式-线上
export const ONLINE_CODE = 'ONLINE';
// 调佣方式-线下
export const OFFLINE_CODE = 'OFFLINE';
// 调佣方式为线上需要置灰的佣金集合
export const DISABLED_COMMISON = [
  // B股
  'bgCommission',
  // 权证
  'qCommission',
  // 担保权证
  'dqCommission'
];

// 调佣方式枚举值配置
export const ADJUECT_COMMISSION_WAY_CONFIG = [
  {
    key: ONLINE_CODE,
    value: '线上',
  },
  {
    key: OFFLINE_CODE,
    value: '线下',
  },
];

// 客户性质-个人
export const CUST_PER = 'per';
// 客户性质-机构（产品）
export const CUST_ORG = 'org';
// 客户性质枚举值配置
export const CUSTTYPE_CONFIG = [
  {
    key: CUST_PER,
    value: '个人',
  },
  {
    key: CUST_ORG,
    value: '机构（产品）',
  },
];

// 佣金费率-股基-参数名
export const GJ_COMMISSION = {
  code: 'targetGJComRatio',
  paramName: 'basicCommission'
};

// 普通账户-佣金费率配置
export const NORMAL_COMMISSION_RATES = {
  targetGJComRatio: {
    brief: '股基',
    paramName: GJ_COMMISSION.paramName,
  },

  HTSC_ZFARE_RATIO: {
    brief: '债券',
    paramName: 'zqCommission',
  },

  HTSC_OFARE_RATIO: {
    brief: '场内基金',
    paramName: 'oCommission',
  },

  HTSC_DFARE_RATIO: {
    brief: '大宗交易',
    paramName: 'dCommission',
  },

  HTSC_QFARE_RATIO: {
    brief: '权证',
    paramName: 'qCommission',
  },

  HTSC_HFARE_RATIO: {
    brief: '回购',
    paramName: 'hCommission',
  },

  HTSC_HKFARE_RATIO: {
    brief: '港股通（净佣金）',
    paramName: 'hkCommission',
  },

  HTSC_STBFARE_RATIO: {
    brief: '股转',
    paramName: 'stbCommission',
  },

  HTSC_BGFARE_RATIO: {
    brief: 'B股',
    paramName: 'bgCommission',
  },
};

// 信用账户-佣金费率配置
export const CREDIT_COMMISSION_RATES = {
  HTSC_DBFARE_RATIO: {
    brief: '担保股基',
    paramName: 'stkCommission',
  },

  HTSC_CBFARE_RATIO: {
    brief: '信用股基',
    paramName: 'creditCommission',
  },

  HTSC_DZFARE_RATIO: {
    brief: '担保债券',
    paramName: 'dzCommission',
  },

  HTSC_CZFARE_RATIO: {
    brief: '信用债券',
    paramName: 'creditBondsCommission',
  },

  HTSC_DOFARE_RATIO: {
    brief: '担保场内基金',
    paramName: 'doCommission',
  },

  HTSC_COFARE_RATIO: {
    brief: '信用场内基金',
    paramName: 'coCommission',
  },

  HTSC_DDFARE_RATIO: {
    brief: '担保大宗交易',
    paramName: 'ddCommission',
  },

  HTSC_DQFARE_RATIO: {
    brief: '担保权证',
    paramName: 'dqCommission',
  }
};

// 账户最低收费设置-定制化最低收费佣金费率配置
export const CUSTOM_MINIMUM_COMMISSION_RATES = {
  gpMinimumCommission: {
    brief: '普通账户股票',
    paramName: 'gpMinimumCommission',
  },
  creditgpMinimumCommission: {
    brief: '信用账户股票',
    paramName: 'creditgpMinimumCommission',
  },
  onExchangeFundCommission: {
    brief: '普通账户场内基金',
    paramName: 'onExchangeFundCommission',
  },
  creditOnExchangeFundCommission: {
    brief: '信用账户场内基金',
    paramName: 'creditOnExchangeFundCommission',
  },
  shNonConvertibleBondsCommission: {
    brief: '普通账户上海债券（不含可转债）',
    paramName: 'shNonConvertibleBondsCommission',
  },
  shCreditNonConvertibleBondsCommission: {
    brief: '信用账户上海债券（不含可转债）',
    paramName: 'shCreditNonConvertibleBondsCommission',
  },
  shConvertibleBondsCommission: {
    brief: '普通账户上海可转债',
    paramName: 'shConvertibleBondsCommission',
  },
  shCreditConvertibleBondsCommission: {
    brief: '信用账户上海可转债',
    paramName: 'shCreditConvertibleBondsCommission',
  },
  szNonConvertibleBondsCommission: {
    brief: '普通账户深圳债券（不含可转债）',
    paramName: 'szNonConvertibleBondsCommission',
  },
  szCreditNonConvertibleBondsCommission: {
    brief: '信用账户深圳债券（不含可转债）',
    paramName: 'szCreditNonConvertibleBondsCommission',
  },
  szConvertibleBondsCommission: {
    brief: '普通账户深圳可转债',
    paramName: 'szConvertibleBondsCommission',
  },
  szCreditConvertibleBondsCommission: {
    brief: '信用账户深圳可转债',
    paramName: 'szCreditConvertibleBondsCommission',
  },
};

// 账户最低收费设置-非定制化最低收费佣金费率配置
export const MINIMUM_COMMISSION_RATES = {
  HTSC_NORMAL_RATIO: {
    brief: '普通账户收取最低费用',
    paramName: 'normalMinimumCharge',
  },

  HTSC_CREDIT_RATIO: {
    brief: '信用账户收取最低费用',
    paramName: 'creditMinimumCharge',
  },
};

// 账户最低收费设置-所有佣金费率配置
export const ALL_MINIMUM_COMMISSION_RATES = {
  ...CUSTOM_MINIMUM_COMMISSION_RATES,
  ...MINIMUM_COMMISSION_RATES
};

// 所有佣金费率
export const ALL_COMMISSION = {
  ...NORMAL_COMMISSION_RATES,
  ...CREDIT_COMMISSION_RATES,
  ...CUSTOM_MINIMUM_COMMISSION_RATES,
  ...MINIMUM_COMMISSION_RATES,
};

// 是否定制化设置最低收费-是
export const SELECT = 'Y';
// 是否定制化设置最低收费-否
export const NO_SELECT = 'N';

// 是否定制化设置最低收费
export const RADIO_LABLE_CONFIG = {
  [SELECT]: '是',
  [NO_SELECT]: '否'
};

// 是否定制化设置最低收费-否-可设置佣金品种配置
export const ONLINE_MINIMUMCHAREG_KEYS = [
  'HTSC_NORMAL_RATIO',
  'HTSC_CREDIT_RATIO',
];

// 申请类型-股权激励code
export const EQUITY_INCENTIVE_CODE = 'equity';
// 申请类型-渠道佣金code
export const CHANNEL_COMMISSION_CODE = 'channel';
// 申请类型-其他code
export const OTHER_CODE = 'complaint';
// 申请类型-只能选择调佣方式为【线上】/客户性质为【个人】的code集合
export const SPECIFIC_APPLY_TYPE_CODES = [EQUITY_INCENTIVE_CODE, 'multiAccount'];
// 申请类型-code集合
export const APPLY_TYPE_CODES = [
  'lowCollectRate',
  'complaint',
  CHANNEL_COMMISSION_CODE,
  'highNetWorth',
  'buyBack'
];

// 默认值
export const DEFAULT_VALUE = '--';

// 费率默认值
export const COMMISSION_DEFAULT_VALUE = '不变';

// 附件信息支持上传格式
export const ATTACH_ACCEPT = '.pdf,.doc,.docx,.xlsx,.xls,.7z,.zip,.rar,.jpg,.png,.jpeg';

// 查询客户列表-新建场景
export const SENSORS_NEW = 'CREATE';
// 查询客户列表-驳回修改场景
export const SENSORS_MODIFY = 'MODIFY';
// 查询客户列表-已存在场景
export const SENSORS_EXISTED = 'EXISTED';

// 场内基金佣金参数code
export const OCOMMISSION_PARAM_CODE = 'oCommission';

// 需要必填标识的佣金类型
export const REQUIRED_PARAMS_CODES = [
  // 场内基金参数code
  OCOMMISSION_PARAM_CODE
];
