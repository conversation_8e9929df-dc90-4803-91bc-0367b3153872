/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-08-20 08:57:00
 */

// 这个table只是简单的将antd的table使用的分页器换为我们自己实现的分页器，完全兼容antd原来的table
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import _ from 'lodash';
import cx from 'classnames';

import DefaultText from '@/components/common/newUI/text/DefaultText';
import { table, dom } from '@/helper';
import Tooltip from '@/components/common/Tooltip';
import Table from './Table';
import PlaceHolderImage from '../placeholderImage';

import styles from './index.less';

export default class CommonTable extends PureComponent {
  static propTypes = {
    // 分页器class
    paginationClass: PropTypes.string,
    // 是否需要在传入数据条数不足时用空行填充
    // !!!如果使用空行填充并且需要前端分页，记得翻页器的total要在外面传，不然组件自己取的是填充过后的数据长度，有问题
    isNeedEmptyRow: PropTypes.bool,
    // table数据一页显示的行数，配合 isNeedEmptyRow 实现数据不满该设定行数时用空行填充
    rowNumber: PropTypes.number,
    // 无数据收的展位图的props
    placeHolderImageProps: PropTypes.object,
    // 外边框是否需要border
    withBorder: PropTypes.bool,
    // 空白间隔列的属性,一般为{ width: 20 },用于控制间隔的宽度
    spaceColumnProps: PropTypes.object,
    // 如果存在固定列，则需要将规定列所在的表格宽度设置成固定的，包含空白咧
    fixedTableWidth: PropTypes.number,
    // 表格新UI规范开关，由于之前有存在使用本table的组件但是还未按照最新的规范修改的表格
    // 为了让样式等不错乱，设置本开关
    useNewUI: PropTypes.bool,
    // 新增一个用来控制表格展开图标所占的列
    // 因为使用新的Table样式，有可能展开那列显示在第一列中，然后UI那边又需要针对展开列做处理
    // 所以增加一个配置,默认有一个样式为expandColumn,为左边距14px,右边距10px
    // 目前只需要提供一个{hasExpand: true}的配置项，如果存在展开行为
    expandColumnProps: PropTypes.object,
    rowSelection: PropTypes.object,
    scroll: PropTypes.object,
    dataSource: PropTypes.array,
    columns: PropTypes.array,
  }

  static defaultProps = {
    paginationClass: '',
    isNeedEmptyRow: false,
    rowNumber: 5,
    placeHolderImageProps: {},
    withBorder: false,
    spaceColumnProps: {},
    fixedTableWidth: 0,
    useNewUI: false,
    expandColumnProps: {},
    rowSelection: null,
    scroll: {},
    dataSource: [],
    columns: [],
  };

  constructor(props) {
    super(props);

    this.state = {
      // 是否设置滚动
      setScroll: !_.isEmpty(props.scroll),
      // 将columns保存到state里面，防止在columns变化的时候能够重新渲染表格数据
      tableColumns: this.setColumns(props),
    };
    // 如果存在固定列的情况下，因为此时antd给与固定列单独一个悬浮的表格来控制，而且给它的宽度为auto;
    // 此时单元格内使用的固定列的数据无法固定，
    // 因此需要给这个固定不动的表格一个固定的宽度，才能是存在固定列滚动的表格样式正常
    // 根据新的UI规范中，每列之间都有一个间隔
    this.tableRef = React.createRef();
  }

  componentDidMount() {
    this.setFixedTableStyle();
  }

  componentDidUpdate(prevProps, prevState) {
    const { columns: prevColumns } = prevProps;
    const { columns: nextColumns } = this.props;
    if (prevColumns !== nextColumns) {
      // 如果Columns变化了，则需要重新设置固定表格的宽度
      // eslint-disable-next-line
      this.setState({
        setScroll: !_.isEmpty(this.props.scroll),
        tableColumns: this.setColumns(this.props)
      }, this.setFixedTableStyle);
    }
  }

  @autobind
  getTableRef() {
    return this.tableRef.current;
  }

  // 获取填充过的数据，判断当前传进来的dataSource是否能在table里显示满（包括前端分页的情况下）
  // 如果无法显示满则用空数据填充至能显示满
  @autobind
  getFilledData() {
    const {
      dataSource,
      rowNumber,
    } = this.props;
    // 需要往原始数据里面填充空数据的条数
    const fillNum = rowNumber - (_.size(dataSource) % rowNumber);
    if (fillNum > 0 && fillNum !== rowNumber) {
      const emptyItemArr = [];
      for (let i = 0; i < fillNum; i++) {
        emptyItemArr.push({
          key: `empty_row_${i}`,
          flag: true,
        });
      }
      return _.concat(dataSource, emptyItemArr);
    }
    return dataSource;
  }

  @autobind
  setColumns(props) {
    const {
      useNewUI,
      spaceColumnProps,
      columns,
      expandColumnProps,
      rowSelection,
    } = props;
    if (useNewUI) {
      return table.padColumnForUI(columns, spaceColumnProps, { className: 'expandColumn', ...expandColumnProps }, rowSelection);
    }
    return columns;
  }

  @autobind
  setFixedTableStyle() {
    const tableNode = this.getTableRef();
    const { fixedTableWidth, useNewUI } = this.props;
    // 如果fixedTableWidth等于0则表示不存在固定列
    // 并且是必须要在新UI规范的情况下
    if (useNewUI && tableNode && fixedTableWidth !== 0) {
      // 一般固定的都是左侧所以，此处暂时设置左侧固定
      const leftFixedTable = tableNode.querySelector('.ant-table-fixed-left table');
      dom.setStyle(leftFixedTable, 'width', `${fixedTableWidth}px`);
      // 需要增加一个当父容器宽度足够放下所有的表格列的时候，不需要滚动设置，
      // 因为如果此时设置了滚动设置，则样式在超宽屏幕下会错乱
      this.fixTableScrollProp();
    }
  }

  @autobind
  fixTableScrollProp() {
    const tableNode = this.getTableRef();
    const { setScroll, tableColumns } = this.state;
    const { fixedTableWidth, useNewUI } = this.props;
    // 只有在需要设置滚动的时候才需要改变
    if (useNewUI && setScroll && tableNode && fixedTableWidth !== 0) {
      // 判断是否能够装下所有的表格列
      // 1.判断所有列是否都是固定宽度的
      const hasAutoWidth = _.find(tableColumns, (column) => _.isUndefined(column.width));
      if (!hasAutoWidth) {
        // 如果没有自动宽度列，则就是所有都是固定宽度列
        // 则如果能够放下所有列，则不需要滚动，并且不需要固定列
        const containerWidth = dom.getRect(tableNode, 'width');
        // 所有固定列加起来的宽度和
        // TODO: 目前暂时没法做到width设置为百分比字符串的形式后的计算
        const allWidth = _.reduce(tableColumns, (sum, col) => sum + col.width, 0);
        if (allWidth <= containerWidth) {
          this.setState({
            setScroll: false,
            tableColumns: _.map(tableColumns, (col) => {
              const { fixed, ...restCol } = col;
              return restCol;
            }),
          });
        }
      }
    }
  }

  render() {
    const {
      paginationClass,
      isNeedEmptyRow,
      dataSource,
      // isNeedNoData,
      // components,
      placeHolderImageProps,
      withBorder,
      columns,
      spaceColumnProps,
      fixedTableWidth,
      useNewUI,
      scroll,
      expandColumnProps,
      ...restProps
    } = this.props;
    const { setScroll, tableColumns } = this.state;
    // 判断当前数据是否空数据
    const isEmptyDataSource = _.isEmpty(dataSource);
    // 如果是空数据，则需要显示无数据展位图，
    // 如果有数据并且需要在不足空行是显示空行，则使用不足空行的方法，不足datasource
    let newDataSource = dataSource;
    if (!isEmptyDataSource && isNeedEmptyRow) {
      newDataSource = this.getFilledData();
    }
    const tableOuterCls = cx({
      [styles.groupTableWithBorder]: withBorder,
      [styles.newGroupTable]: true,
    });
    return (
      <div className={tableOuterCls} ref={this.tableRef}>
        <Table
          {...restProps}
          scroll={{ x: '100%' }}
          columns={tableColumns}
          dataSource={newDataSource}
          paginationClass={`${styles.pagination} ${paginationClass}`}
          locale={
            {
              emptyText: <PlaceHolderImage {...placeHolderImageProps} />
            }
          }
        />
      </div>
    );
  }
}

// 表格提示单元格
export function ToolTipCell(props) {
  const {
    tipContent,
    cellText,
    cellClass,
    placement,
    clickable,
  } = props;
  // 省略号，并且可以设置点击
  const cellCls = cx(cellClass, {
    [styles.textEllipse]: true,
    [cellClass]: true,
    [styles.clickable]: clickable
  });
  return (
    <Tooltip title={tipContent} placement={placement}>
      <div className={cellCls}>
        <DefaultText text={cellText} />
      </div>
    </Tooltip>
  );
}

ToolTipCell.propTypes = {
  // 提示框内容
  tipContent: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
    PropTypes.node,
  ]),
  // 单元格内容
  cellText: PropTypes.node,
  // 单元格额外class
  cellClass: PropTypes.string,
  placement: PropTypes.string,
  // 是否可点击
  clickable: PropTypes.bool,
};

ToolTipCell.defaultProps = {
  cellClass: '',
  placement: 'topLeft',
  clickable: false,
  tipContent: '',
  cellText: null,
};
