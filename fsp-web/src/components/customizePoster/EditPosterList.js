/*
 * @Author: lijingjing
 * @Date: 2022-04-26 10:20:01
 * @description 自定义海报编辑列表
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import {
  isEmpty, map, isNull, filter, size, isNumber, find, omit, isEqual
} from 'lodash';
import moment from 'moment';
import { Button, message } from 'antd';
import IFWrap from '@/components/common/IFWrap';
import Modal from '@/components/common/newUI/modal';
import { logCommon } from '@/decorators/logable';
import confirm from '@/components/common/newUI/confirm';
import Table, { ToolTipCell } from '@/components/common/table';
import { number } from '@/helper';
import Carousel from './Carousel';
import AddPosterForm from './AddPosterForm';
import delIcon from './images/del.svg';
import upIcon from './images/up.svg';
import upDisabledIcon from './images/upDisabled.svg';
import downIcon from './images/down.svg';
import downDisabledIcon from './images/downDisabled.svg';
import editIcon from './images/edit.svg';

import styles from './editPosterList.less';

import {
  ADD,
  UPDATE,
  DELETE,
  POSTER_TABLE_COLUMNS,
  COVER_KEY,
  UPLOAD_TIME_KEY,
  OPTION_KEY,
  FORWARD_TIMES_KEY,
} from './config';

import { BIRTHDAY_TAB_KEY, FESTIVAL_TAB_KEY } from '../../routes/customizePoster/config';

import { swapArray } from './utils';

export default class EditPosterList extends Component {
  static propTypes = {
    // 海报列表
    posterList: PropTypes.array.isRequired,
    // 编辑海报列表
    editPosterList: PropTypes.func.isRequired,
    // 海报类型
    type: PropTypes.string.isRequired,
    // 节日id
    festivalId: PropTypes.string,
    queryPosterList: PropTypes.func.isRequired,
  }

  static defaultProps = {
    festivalId: '',
  };

  static contextTypes = {
    goBack: PropTypes.func.isRequired,
  }

  constructor(props, context) {
    super(props);
    this.state = {
      // 当前海报列表
      currentPosterList: props.posterList,
      // 是否展示新建海报弹框
      showEditPosterModal: false,
      currentEditData: {},
      deleteList: [],
    };
    this.columns = this.getTransColumn(POSTER_TABLE_COLUMNS);
    this.formRef = null;
  }

  componentDidMount() {
  }

  componentDidUpdate(prevProps) {
    if (prevProps.posterList !== this.props.posterList) {
      this.updatePosterList();
    }
  }

  @autobind
  updatePosterList() {
    this.setState({
      currentPosterList: this.props.posterList,
    });
  }

  @autobind
  getTransColumn(columns) {
    return map(columns, (column) => {
      const { key } = column;
      if (key === COVER_KEY) {
        // 图片信息
        return this.updateCoverColumn(column);
      }
      if (key === UPLOAD_TIME_KEY) {
        // 上传时间
        return this.updateTimeColumn(column);
      }
      if (key === OPTION_KEY) {
        // 操作栏
        return this.updateOptionColumn(column);
      }
      if (key === FORWARD_TIMES_KEY) {
        return this.updateForwardTimesColumn(column);
      }
      return this.updateWordColumn(column);
    });
  }

  @autobind
  updateCoverColumn(column) {
    return {
      ...column,
      render: (cover, record) => {
        if (cover === '' || isNull(cover)) {
          return '--';
        }

        return <img className={styles.coverImg} src={cover} />;
      },
    };
  }

  @autobind
  updateTimeColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (text === '' || isNull(text)) {
          return '--';
        }

        return moment(text).format('YYYY-MM-DD HH:mm');
      },
    };
  }

  @autobind
  updateForwardTimesColumn(column) {
    return {
      ...column,
      render: (text, record) => {
        if (text === '' || isNull(text)) {
          return 0;
        }

        return number.thousandFormat(text, false);
      }
    };
  }

  @autobind
  updateOptionColumn(column) {
    return {
      ...column,
      render: (text, record, index) => {
        const { currentPosterList } = this.state;
        return (
          <>
            <IFWrap when={index === 0}>
              <img src={upDisabledIcon} className={styles.icon} />
            </IFWrap>
            <IFWrap when={index > 0}>
              <img
                src={upIcon}
                className={styles.icon}
                onClick={() => this.handleOptUpClick(index)}
              />
            </IFWrap>
            <IFWrap when={index < size(currentPosterList) - 1}>
              <img
                src={downIcon}
                className={styles.icon}
                onClick={() => this.handleOptDownClick(index)}
              />
            </IFWrap>
            <IFWrap when={index === size(currentPosterList) - 1}>
              <img src={downDisabledIcon} className={styles.icon} />
            </IFWrap>
            <img
              className={styles.icon}
              src={editIcon}
              onClick={() => this.handleOptEditClick(index)}
            />
            <img
              className={styles.icon}
              src={delIcon}
              onClick={() => this.handleOptDelClick(index)}
            />
          </>
        );
      },
    };
  }

  @autobind
  getPosterType() {
    const { type } = this.props;
    return type === BIRTHDAY_TAB_KEY ? '生日海报' : '节日海报';
  }

  @autobind
  handleOptUpClick(currentIndex) {
    const { currentPosterList } = this.state;
    // optStatus字段后端不会返回
    // 如果目标选中数据的不是新建数据，则需把其optStatus置为UPDATE
    if (currentPosterList[currentIndex].optStatus !== ADD) {
      currentPosterList[currentIndex].optStatus = UPDATE;
    }
    // // 如果目标选中数据的上一条数据不是新建数据，则需把其optStatus置为UPDATE
    if (currentPosterList[currentIndex - 1].optStatus !== ADD) {
      currentPosterList[currentIndex - 1].optStatus = UPDATE;
    }
    this.setState({
      currentPosterList: swapArray(currentPosterList, currentIndex - 1, currentIndex)
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-上移` }
    });
  }

  @autobind
  handleOptDownClick(currentIndex) {
    const { currentPosterList } = this.state;
    // optStatus字段后端不会返回
    // 如果目标选中数据的不是新建数据，则需把其optStatus置为UPDATE
    if (currentPosterList[currentIndex].optStatus !== ADD) {
      currentPosterList[currentIndex].optStatus = UPDATE;
    }
    // 如果目标选中数据的下一条数据不是新建数据，则需把其optStatus置为UPDATE
    if (currentPosterList[currentIndex + 1].optStatus !== ADD) {
      currentPosterList[currentIndex + 1].optStatus = UPDATE;
    }

    this.setState({
      currentPosterList: swapArray(currentPosterList, currentIndex, currentIndex + 1)
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-下移` }
    });
  }

  @autobind
  handleOptEditClick(currentIndex) {
    const { currentPosterList } = this.state;
    this.setState({
      showEditPosterModal: true,
      currentEditData: currentPosterList[currentIndex],
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-编辑` }
    });
  }

  @autobind
  delTargetItem(targetItem, currentIndex) {
    const { currentPosterList, deleteList } = this.state;
    // 新建后直接删除的数据提交时不传给后端
    if (targetItem.optStatus !== ADD || !targetItem.optStatus) {
      this.setState({
        // 如删除数据，则被删数据的后面数据optStatus也要置为update
        currentPosterList: map(currentPosterList, (item, index) => {
          if (index === currentIndex) {
            // 当前的数据需要删除
            return null;
          }

          if (index > currentIndex && item.optStatus !== ADD) {
            return {
              ...item,
              optStatus: UPDATE,
            };
          }
          return item;
        }).filter(Boolean),

        deleteList: [...deleteList, { ...targetItem, optStatus: DELETE }]
      });
    } else {
      this.setState({
        currentPosterList: filter(currentPosterList, (value, index) => index !== currentIndex),
      });
    }

    message.success('删除成功！');
  }

  @autobind
  handleOptDelClick(currentIndex) {
    const { currentPosterList } = this.state;
    const targetItem = find(currentPosterList,
      (item, index) => index === currentIndex) || {};

    confirm({
      title: '',
      content: '是否删除该模块',
      okText: '确认',
      cancelText: '取消',
      onOk: () => this.delTargetItem(targetItem, currentIndex),
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-删除` }
    });
  }

  @autobind
  updateWordColumn(column) {
    return {
      ...column,
      render: (text, record) => this.renderCell(text, record),
    };
  }

  @autobind
  renderCell(text, record, clickable = false) {
    if (text === '' || isNull(text)) {
      return '--';
    }
    return (
      <ToolTipCell
        clickable={clickable}
        tipContent={text}
        cellText={text}
      />
    );
  }

  @autobind
  handleCancelClick() {
    confirm({
      title: '',
      content: '取消后不会保存当前修改，请确认是否取消',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.updatePosterList();
        const { type } = this.props;
        if (type === FESTIVAL_TAB_KEY) {
          this.context.goBack();
        }
      }
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-取消` }
    });
  }

  @autobind
  async handleSubmitClick() {
    const { deleteList, currentPosterList } = this.state;
    const {
      editPosterList,
      posterList,
      type,
      festivalId,
      queryPosterList,
    } = this.props;
    if (!isEqual(currentPosterList, posterList)) {
      // 去除新增数据的id，将新增数据id置为空
      const currentPosterLists = map(currentPosterList, (item) => {
        if (item.optStatus === ADD) {
          return omit(item, 'id');
        }
        return item;
      });
      this.setState({
        currentPosterList: currentPosterLists,
      });

      const res = await editPosterList({
        list: [...currentPosterLists, ...deleteList],
        type,
        festivalId,
      });

      if (res) {
        message.success('提交成功！');
        this.setState({
          deleteList: [],
        });
        queryPosterList();
      }
    }
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-确定` }
    });
  }

  @autobind
  handlePosterModalClose() {
    this.setState({
      showEditPosterModal: false,
      currentEditData: {},
    });

    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-新建${this.getPosterType()}-取消` }
    });
  }

  @autobind
  handlePosterModalConfirm() {
    const { currentPosterList, currentEditData } = this.state;
    this.formRef.validateFieldsAndSave().then((res) => {
      const uploadTime = moment().valueOf();
      // 新建
      if (isEmpty(res.id) && !isNumber(res.id)) {
        this.setState({
          currentPosterList: [
            ...currentPosterList,
            {
              ...res,
              status: '待提交',
              optStatus: ADD,
              // uploadTime,
              id: Math.floor(Math.random() * Math.random() * 1000000),
            }],
        });
      } else if (currentEditData !== res) {
        // 编辑
        this.setState({
          currentPosterList: map(currentPosterList, (item) => {
            if (item.id === res.id && (item.optStatus !== ADD)) {
              return ({
                ...res, status: '待提交', uploadTime, optStatus: UPDATE
              });
            } if (item.id === res.id && item.optStatus === ADD) {
              return ({
                ...res, status: '待提交', uploadTime, optStatus: ADD
              });
            }
            return item;
          }),
        });
      }
      this.setState({
        showEditPosterModal: false,
        currentEditData: {},
      });
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-新建${this.getPosterType()}-确定` }
    });
  }

  @autobind
  handleAddClick() {
    this.setState({
      showEditPosterModal: true,
    });
    logCommon({
      type: 'Click',
      payload: { name: `问候海报配置-${this.getPosterType()}-跳转新建海报` }
    });
  }

  render() {
    const {
      currentPosterList,
      showEditPosterModal,
      currentEditData,
    } = this.state;
    const { type } = this.props;

    return (
      <div className={styles.editPosterListWrap}>
        <IFWrap when={!isEmpty(currentPosterList)}>
          <div className={styles.previewWrap}>
            <div className={styles.title}>
              效果预览
            </div>
            <div className={styles.slideContent}>
              <Carousel list={currentPosterList} type={type} />
            </div>
          </div>
        </IFWrap>
        <div className={styles.toolBar}>
          <Button type="primary" icon="plus" onClick={this.handleAddClick}>新建海报</Button>
        </div>
        <Table
          key="editPosterTable"
          rowKey="id"
          columns={this.columns}
          dataSource={currentPosterList}
          spaceColumnProps={{ width: '130px' }}
          useNewUI
          placeHolderImageProps={{
            title: '暂无海报，快去添加海报吧'
          }}
          pagination={false}
        />
        <div className={styles.buttonGroup}>
          <Button onClick={this.handleCancelClick}>取消</Button>
          <Button type="primary" onClick={this.handleSubmitClick}>提交</Button>
        </div>
        <IFWrap when={showEditPosterModal}>
          <Modal
            visible
            size="small"
            modalKey="editPosterModal"
            title={`${isEmpty(currentEditData) ? '新建' : '编辑'}海报`}
            onModalClose={this.handlePosterModalClose}
            modalFooter={[
              {
                key: 'posterCloseBtn',
                text: '取消',
                onClick: this.handlePosterModalClose,
              },
              {
                key: 'posterConfirmBtn',
                text: '确定',
                type: 'primary',
                onClick: this.handlePosterModalConfirm,
              }
            ]}
          >
            <AddPosterForm
              data={currentEditData}
              wrappedComponentRef={(form) => this.formRef = form} // eslint-disable-line
              showName
              type={type}
            />
          </Modal>
        </IFWrap>
      </div>
    );
  }
}
