/**
 * @Author: liji<PERSON>jing
 * @Description: 新建海报
 * @Last Modified by: sunweibin
 * @Last Modified time: 2022-05-30 09:46:04
 * @Date: 2022-04-27 14:50:21
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import {
  Form, Input, message, Radio
} from 'antd';
import { emp, data as dh } from '@/helper';
import { logCommon } from '@/decorators/logable';
import Uploader from '@crm/uploader';
import _ from 'lodash';
import { UPLOAD_IMG_WIDTH, UPLOAD_IMG_HEIGHT } from './config';

import styles from './addPosterForm.less';

const { create } = Form;
const FormItem = Form.Item;
// 附件上传路径
const UPLOADER_ACTION = '/fspa/aorta/dmz/api/storage/oss/upload';

@create()
export default class AddPosterForm extends PureComponent {
  static propTypes = {
    form: PropTypes.object.isRequired,
    data: PropTypes.object.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {};
    this.uploaderKey = dh.uuid();
  }

  static defaultProps = {};

  @autobind
  validateFieldsAndSave() {
    const {
      form: { validateFields },
      data,
    } = this.props;
    return new Promise((resolve) => {
      validateFields((errors, values) => {
        if (_.isEmpty(errors)) {
          resolve({ ...data, ...values });
        }
      });
    });
  }

  @autobind
  handleUploadSuccess(apiResult) {
    const res = apiResult.resultData;
    const {
      form: { setFieldsValue },
    } = this.props;

    setFieldsValue({
      materialFileUrl: res?.data?.ossDownUrl,
      uploadTime: res?.data?.uploadTimestamp
    });
    return Promise.resolve(res);
  }

  @autobind
  handleUploadRemove(apiResult) {
    const { attachId, status } = apiResult;
    const {
      form: { setFieldsValue },
    } = this.props;
    if (!_.isEmpty(attachId) && status !== 'error') {
      setFieldsValue({
        materialFileUrl: null,
        uploadTime: null
      });
    }
    return Promise.resolve(true);
  }

  @autobind
  checkImageWH(file, width, height) {
    return new Promise((resolve, reject) => {
      const filereader = new FileReader();
      filereader.onload = (e) => {
        const src = e.target.result;
        const image = new Image();
        // eslint-disable-next-line func-names
        image.onload = function () {
          if (
            (width && this.width !== width)
            || (height && this.height !== height)
          ) {
            message.error(
              `尺寸仅能上传${UPLOAD_IMG_WIDTH}*${UPLOAD_IMG_HEIGHT}`
            );
            reject();
          } else {
            resolve();
          }
        };
        image.onerror = reject;
        image.src = src;
      };
      filereader.readAsDataURL(file);
    });
  }

  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const {
      materialName, materialFileUrl, materialType, materialDesc, uploadTime
    } = this.props.data || {};

    return (
      <div className={styles.addPosterFormWrap}>
        <Form className={styles.formContent}>
          <div className={styles.content}>
            <FormItem label="素材分类" className={styles.inputItem}>
              {getFieldDecorator('materialType', {
                initialValue: materialType || '全量客户',
                rules: [
                  {
                    required: true,
                    message: '素材分类不能为空',
                    whitespace: true,
                  },

                ],
              })(
                <Radio.Group className={styles.radioBox}>
                  <Radio.Button value="全量客户">全量客户</Radio.Button>
                  <Radio.Button value="风险适配">风险适配</Radio.Button>
                </Radio.Group>
              )}
            </FormItem>
            <FormItem label="海报名称" className={styles.inputItem}>
              {getFieldDecorator('materialName', {
                initialValue: materialName,
                rules: [
                  {
                    required: true,
                    message: '海报名称不能为空',
                    whitespace: true,
                  },
                  {
                    max: 8,
                    message: '长度不能超过8个字',
                  },
                ],
              })(<Input placeholder="请输入海报名称，不超过8个字" />)}
            </FormItem>
            <FormItem label="素材上传" className={styles.inputItem}>
              {getFieldDecorator('materialFileUrl', {
                initialValue: materialFileUrl,
                rules: [
                  {
                    required: true,
                    message: '图片不能为空',
                  },
                ],
              })(<Input type="hidden" />)}
              <Uploader
                key={this.uploaderKey}
                action={`${UPLOADER_ACTION}?empId=${emp.getId()}`}
                padFileInfo={() => ({ creator: emp.getId() })}
                data={{
                  empId: emp.getId(),
                  attachment: dh.uuid(),
                }}
                headers={{
                  empId: emp.getId()
                }}
                accept=".png,.jpeg,.jpg"
                attachmentCountLimit={1}
                showEmptyPlaceHolder={false}
                getAttachDownloadURL={(v) => v}
                onSuccess={this.handleUploadSuccess}
                onRemove={this.handleUploadRemove}
              />
            </FormItem>
            <FormItem label="素材说明" className={styles.inputItem}>
              {getFieldDecorator('materialDesc', {
                initialValue: materialDesc,
                rules: [
                  {
                    required: false,
                    message: '素材说明不能为空',
                    whitespace: true,
                  },
                  {
                    max: 50,
                    message: '长度不能超过50个字',
                  },
                ],
              })(<Input placeholder="请输入素材说明，不超过50个字" />)}
            </FormItem>
            <FormItem label="上传时间" className={styles.inputItem} style={{ display: 'none' }}>
              {getFieldDecorator('uploadTime', {
                initialValue: uploadTime,
                rules: [
                  {
                    required: false,
                    message: '素材说明不能为空',
                    whitespace: true,
                  }
                ],
              })(<Input placeholder="" />)}
            </FormItem>
          </div>
        </Form>
      </div>
    );
  }
}
