/*
 * @Author: lijingjing
 * @Date: 2022-04-26 10:20:01
 * @description 自定义海报编辑-海报预览banner
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import { logCommon } from '@/decorators/logable';
import {
  map, size
} from 'lodash';
import {
  emp
} from '@/helper';
import { Icon } from 'antd';
import IFWrap from '@/components/common/IFWrap';
import ExpendImg from './expendImg';
import styles from './carousel.less';

export default class EditPosterList extends PureComponent {
  static propTypes = {
    list: PropTypes.array.isRequired,
  }

  constructor(props, context) {
    super(props);
    this.state = {
      currentIndex: 0,
      currentImgUrl: '',
      flag: false
    };
  }

  componentDidMount() {

  }

  componentDidUpdate(prevProps, prevState) {
    // 比较新旧状态
    if (this.state.flag) {
      document.addEventListener('wheel', this.handleScroll, { passive: false });
      document.addEventListener('touchmove', this.handleScroll, { passive: false });
    } else {
      document.removeEventListener('wheel', this.handleScroll);
      document.removeEventListener('touchmove', this.handleScroll);
    }
  }

  handleScroll(event) {
    event.preventDefault();
  }

  @autobind
  handleDotClick(index) {
    this.setState({
      currentIndex: index,
    });
  }

  @autobind
  handleArrowLeftClick() {
    const { currentIndex } = this.state;
    this.setState({
      currentIndex: currentIndex - 1,
    });
  }

  @autobind
  handleArrowRightClick() {
    const { currentIndex } = this.state;
    this.setState({
      currentIndex: currentIndex + 1,
    });
  }

  render() {
    const {
      list,
    } = this.props;
    const { currentIndex, currentImgUrl, flag } = this.state;
    return (
      <>
        <div className={styles.carouselWrap}>
          <div className={styles.sliderList}>
            <div
              className={styles.sliderBox}
              style={{
                width: `${200 + (175 * (size(list) - 1))}px`,
                transform: `translate3d(${175 - (currentIndex * 175)}px, 0, 0)`,
              }}
            >
              {
              map(list, (item, index) => (
                <div
                  className={classnames({
                    [styles.carouselItem]: true,
                    [styles.active]: currentIndex === index,
                  })}
                  key={index}
                >
                  <div className={styles.title}>{item.materialName}</div>
                  <img
                    src={item.materialFileUrl}
                    onClick={() => {
                      if (currentIndex === index) {
                        this.setState({
                          currentImgUrl: item.materialFileUrl,
                          flag: true
                        });
                      }
                    }}
                  />
                </div>
              ))
            }
            </div>
          </div>

          <div className={styles.arrowGroup}>
            <IFWrap when={currentIndex > 0}>
              <div className={styles.arrowLeft} onClick={this.handleArrowLeftClick}>
                <Icon type="left" />
              </div>
            </IFWrap>
            <IFWrap when={currentIndex < (size(list) - 1)}>
              <div className={styles.arrowRight} onClick={this.handleArrowRightClick}>
                <Icon type="right" />
              </div>
            </IFWrap>
          </div>
          <div className={styles.carouselDot}>
            {
            map(list, (item, index) => (
              <span
                key={index}
                className={classnames({
                  [styles.dot]: true,
                  [styles.active]: currentIndex === index,
                })}
                onClick={() => this.handleDotClick(index)}
              />
            ))
          }
          </div>
        </div>
        {flag && (
        <ExpendImg
          imgUrl={currentImgUrl}
          onClose={() => {
            this.setState({
              flag: false
            });
          }}
        />
        )}
      </>
    );
  }
}
