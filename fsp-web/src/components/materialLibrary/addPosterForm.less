.addPosterFormWrap {
  margin-top: 20px;
  .tip {
    font-size: 14px;
    color: #999;
  }
  .radioBox {
    label {
      margin-right: 20px;
      background: rgba(128, 128, 128, 10%);
      border-radius: 16px;
      border: none;
    }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
      .ant-radio-button-wrapper-checked {
        box-shadow: none;
        background: rgba(16, 142, 233, 10%);
      }
      .ant-radio-button-wrapper:not(:first-child)::before {
        width: 0;
      }
    }
  }
    /* stylelint-disable-next-line selector-pseudo-class-blacklist */
    :global {
    .ant-uploader {
      display: inline-block;
      width: auto;
      min-height: auto;
    }
    .ant-form-item {
      display: flex;
      margin-bottom: 10px;
    }
    .ant-form-item-label {
      color: #999;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
    .ant-input {
      width: 272px;
    }
  }
}
