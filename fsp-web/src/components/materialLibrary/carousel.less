.carouselWrap {
  position: relative;
  padding: 0 40px 30px;

  .arrowLeft, .arrowRight {
    width: 30px;
    height: 27px;
    background: rgba(102, 102, 102, 0.4);
    border-radius: 6px;
    color: #fff;
    text-align: center;
    line-height: 27px;
    cursor: pointer;
  }

  .arrowLeft {
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -316px;
    margin-top: -30px;
  }
  .arrowRight {
    position: absolute;
    right: 50%;
    top: 50%;
    margin-right: -315px;
    margin-top: -30px;
  }

  .carouselDot {
    text-align: center;
    font-size: 0;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;

    .dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #000;
      opacity: 0.2;
      border-radius: 50%;
      cursor: pointer;
      margin-left: 5px;

      &.active {
        background: #108ee9;
        opacity: 1;
      }
    }
  }

  .sliderList {
    width: 550px;
    height: 350px;
    font-size: 0;
    overflow: hidden;
    align-items: center;
    margin: 0 auto;

    .sliderBox {
      width: 2000000px;
      transform: translate3d(160px, 0, 0);
    }

    .carouselItem {
      width: 160px;
      height: 280px;
      position: relative;
      display: inline-block;
      border-radius: 6px;
      overflow: hidden;
      vertical-align: middle;

      & + .carouselItem {
        margin-left: 15px;
      }

      .contentWrap {
        position: absolute;
        right: 0;
        bottom: 5px;
        left: 0;
        color: #fff;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0;
        transform: scale(.7);

        .content {
          text-indent: 2em;
        }

        .footer {
          text-align: right;
        }
      }

      img {
        width: 100%;
        height: 100%;
      }

      &.active {
        width: 200px;
        height: 350px;

        .title {
          height: 38px;
          line-height: 38px;
          font-size: 18px;
        }
      }

      .title {
        height: 34px;
        line-height: 34px;
        background: rgba(0, 0, 0, .3);
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        font-size: 14px;
        text-align: center;
        width: 100%;
      }
    }
  }
}
