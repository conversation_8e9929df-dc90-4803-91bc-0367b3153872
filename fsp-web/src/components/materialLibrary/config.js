// 海报名称
export const POSTER_NAME_KEY = 'materialName';

// 图片信息
export const COVER_KEY = 'materialFileUrl';

// 上传时间
export const UPLOAD_TIME_KEY = 'uploadTime';

// 素材说明
export const FORWARD_TIMES_KEY = 'materialDesc';
// 素材分类
export const FORWARD_TIMES_TYPE = 'materialType';
// 状态key
export const STATUS_KEY = 'status';

// 操作key
export const OPTION_KEY = 'option';

// 新增状态
export const ADD = 'add';

// 更新状态
export const UPDATE = 'update';

// 删除状态
export const DELETE = 'delete';

//  海报表格列
export const POSTER_TABLE_COLUMNS = [
  {
    key: POSTER_NAME_KEY,
    dataIndex: POSTER_NAME_KEY,
    title: '海报名称',
    width: 120,
    tooltip: false,
  },
  {
    key: COVER_KEY,
    dataIndex: COVER_KEY,
    title: '海报信息',
    maxWidth: 100,
    tooltip: false,
  },
  {
    key: UPLOAD_TIME_KEY,
    dataIndex: UPLOAD_TIME_KEY,
    title: '上传时间',
    maxWidth: 90,
    tooltip: false,
  },
  {
    key: FORWARD_TIMES_KEY,
    dataIndex: FORWARD_TIMES_KEY,
    title: '素材说明',
    width: 150,
    tooltip: false,
  },
  {
    key: FORWARD_TIMES_TYPE,
    dataIndex: FORWARD_TIMES_TYPE,
    title: '素材分类',
    width: 80,
    tooltip: false,
  },
  {
    key: STATUS_KEY,
    dataIndex: STATUS_KEY,
    title: '状态',
    width: 60,
    tooltip: false,
  },
  {
    key: OPTION_KEY,
    dataIndex: OPTION_KEY,
    title: '操作',
    width: 130,
    tooltip: false,
  },
];

// 上传图片宽度大小限制
export const UPLOAD_IMG_WIDTH = 375;

// 上传图片长度大小限制
export const UPLOAD_IMG_HEIGHT = 702;
