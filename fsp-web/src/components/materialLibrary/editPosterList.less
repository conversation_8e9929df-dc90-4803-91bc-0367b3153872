.editPosterListWrap {
  .previewWrap {
    padding: 20px;
    height: 450px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    margin-bottom: 20px;

    .title {
      font-size: 14px;
      background: url(./images/preview_bg.png) no-repeat left bottom;
      background-size: 80px 8px;
    }

    .posterItem {
      width: 200px;
      height: 350px;

      img {
        width: 100%;
      }
    }
  }
  .descBox {
    width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
  .noPerview {
    padding: 20px;
    height: 450px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img {
      margin-bottom: 20px;
    }
  }
  .coverImg {
    width: 100px;
    height: 160px;
  }

  .toolBar {
    padding-bottom: 20px;
  }

  .icon {
    color: #2d6dd4;
    margin-right: 18px;
    cursor: pointer;
  }

  /* stylelint-disable-next-line selector-pseudo-class-blacklist */
  :global {
    .ant-table-tbody > tr > td {
      text-align: center;
      padding: 20px 0;
    }

    .ant-table-thead > tr > th {
      text-align: center;
      padding: 20px 0;
    }

    .ant-tabs-nav .ant-tabs-tab {
      font-size: 14px;
    }
  }
  .buttonGroup {
    text-align: right;
    margin-top: 30px;
  }
}

