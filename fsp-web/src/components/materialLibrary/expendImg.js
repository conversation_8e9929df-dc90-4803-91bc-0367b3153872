import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Icon } from 'antd';
import styles from './expendImg.less';

export default class ExpendImg extends PureComponent {
  static propTypes = {
    imgUrl: PropTypes.string.isRequired,
    onClose: PropTypes.func.isRequired
  }

  constructor(props, context) {
    super(props);
  }

  render() {
    const {
      imgUrl,
      onClose
    } = this.props;
    return (
      <>
        <div className={styles.expendImgBox}>
          <Icon
            type="close"
            onClick={onClose}
            className={styles.closeIcon}
          />
          <img src={imgUrl} alt="" />
        </div>
      </>
    );
  }
}
