/* eslint-disable import/no-anonymous-default-export */
/*
 * @Author: lijingjing
 * @Date: 2022-04-25 17:22:52
 * @Last Modified by: weiting
 * @Last Modified time: 2022-07-13 17:52:16
 */
import { map } from 'lodash';
import moment from 'moment';
import { materialLibrary as api } from '../api';

const EMPTY_ARRAY = [];
export default {
  namespace: 'materialLibrary',
  state: {
    posterList: EMPTY_ARRAY,
  },
  reducers: {
    queryPosterListSuccess(state, action) {
      const { payload } = action;
      const transList = map(payload, (item, index) => (
        {
          ...item,
          id: index,
          status: '已生效',
        }
      ));
      return {
        ...state,
        posterList: transList,
      };
    },
  },
  effects: {
    // 获取海报列表
    * queryPosterList({ payload }, { call, put }) {
      const response = yield call(api.queryPosterList, payload);
      yield put({
        type: 'queryPosterListSuccess',
        payload: response?.resultData || [],
      });
    },
    // 保存素材海报
    * savePosterList({ payload }, { call, put }) {
      const response = yield call(api.savePosterList, payload);
      return response;
    },
  },
  subscriptions: {},
};
