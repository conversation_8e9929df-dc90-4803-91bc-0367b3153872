// 客户列表配置列
export const CUSTOMER_COLUMNS = [
  {
    dataIndex: 'custId',
    key: 'custId',
    title: '客户号',
    width: 110,
  },
  {
    dataIndex: 'custName',
    key: 'custName',
    title: '客户名称',
    width: 90,
  },
  {
    dataIndex: 'channelName',
    key: 'channelName',
    title: '渠道标识',
    width: 120,
  },
  {
    dataIndex: 'activeTime',
    key: 'activeTime',
    title: '激活时间',
    width: 120,
  },
  {
    dataIndex: 'serviceOrgName',
    key: 'serviceOrgName',
    title: '服务营业部',
    width: 110,
  },
  {
    dataIndex: 'openOrgName',
    key: 'openOrgName',
    title: '开户营业部',
    width: 110,
  },
  {
    dataIndex: 'unifiedCommRaiseFlag',
    key: 'unifiedCommRaiseFlag',
    title: '是否账户升佣客户',
    width: 98,
  },
  {
    dataIndex: 'zlPickInvestFlag',
    key: 'zlPickInvestFlag',
    title: '是否涨乐选投顾',
    width: 98,
  },
  {
    dataIndex: 'zlPickInvestDate',
    key: 'zlPickInvestDate',
    title: '最近一次涨乐选投顾时间',
    width: 149,
  },
];

// 默认值
export const DEFAULT_VALUE = '--';

// 是否设置多档最低收费-是
export const SELECT = 'Y';
// 是否设置多档最低收费-否
export const NO_SELECT = 'N';

// 渠道调佣审批流-需要选择驳回类型的审批节点集合
export const CHANNEL_REJECT_NODES = [
  '分公司专岗审批(一体化对接)',
  '驳回到分公司专岗审批',
  '平台运营部管理岗审核',
  '驳回到平台运营部管理岗审核',
  '投顾发展部业务管理岗审核',
  '业务主管部佣金复核',
  '驳回到分公司佣金管理岗审批',
];
// 非渠道调佣审批流-需要选择驳回类型的审批节点集合
export const UN_CHANNEL_REJECT_NODES = [
  '分公司佣金管理岗审批',
  '驳回到分公司专岗审批',
  '驳回到业务主管部门审核（个人）',
  '驳回到业务主管部门审核（机构）',
  '驳回到分公司佣金管理岗审批',
  '业务主管部门审核（个人）',
  '业务主管部门审核（机构）',
];

// 非渠道调佣审批流-需要会签的审批节点集合
export const UN_CHANNEL_APPROVAL_NODES = [
  '业务主管部门审核（个人）',
  '业务主管部门审核（机构）',
];
// 送会签-code
export const COUNTERSING_CODE = '1';
// 不需要会签-code
export const UN_COUNTERSING_CODE = '0';
