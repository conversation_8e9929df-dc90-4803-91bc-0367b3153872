/*
 * @Author: yanfaping
 * @Date: 2024-08-27 14:35:18
 * @LastEditors: yanfaping
 * @LastEditTime: 2025-09-18 09:08:33
 * @Description: 佣金授权申请-审批页面
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import classnames from 'classnames';
import {
  DetailBlock,
  InfoCell,
  InfoGroup,
  FlowButtons,
} from '@crm/biz-ui';
import { message, Radio } from 'antd';
import { sensors } from '@lego/bigbox-utils';

import withRouter from '@/decorators/withRouter';
import { commissionAuthorization as api } from '@/api/approval';
import Table, { ToolTipCell } from '@/components/common/table';
import Pagination from '@/components/common/Pagination';
import Uploader from '@/newUI/upload';
import confirm from '@/components/common/newUI/confirm';
import ApprovalHistory from '@/components/common/ApprovalHistory';
import {
  NORMAL_COMMISSION_RATES,
  CREDIT_COMMISSION_RATES,
  CUSTOM_MINIMUM_COMMISSION_RATES,
  MINIMUM_COMMISSION_RATES,
  CHANNEL_COMMISSION_CODE,
  COMMISSION_DEFAULT_VALUE,
  SENSORS_MODIFY,
  REQUIRED_PARAMS_CODES,
  OTHER_CODE
} from '@/components/commissionAuthorization/config';
import { viewFilePdf } from '@/components/commissionAuthorization/utils';
import Loading from '@/layouts/Loading';
import IFWrap from '@/components/common/IFWrap';

import RejectModal from './RejectModal';
import CountersignModal from './CountersignModal';
import {
  CUSTOMER_COLUMNS,
  DEFAULT_VALUE,
  SELECT,
  NO_SELECT,
  UN_CHANNEL_APPROVAL_NODES,
  UN_COUNTERSING_CODE,
} from './config';
import styles from './index.less';

const { logCommon } = sensors;
const RadioGroup = Radio.Group;

// 需要传分公司orgId的审批节点
const currentStepNames = ['服务营业部负责人审批', '开户营业部负责人审批'];

@withRouter
export default class CommissionAuthorizationApproval extends PureComponent {
  static propTypes = {
    location: PropTypes.object.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
      // 佣金授权申请详情数据
      detailData: {},
      // 审批历史数据
      flowHistory: {},
      // 审批按钮
      flowButtons: [],
      // 是否显示驳回弹框
      rejectModalVisible: false,
      // 是否显示会签人选择弹框
      countersignModalVisible: false,
      // 客户信息
      custData: {},
      // 账户最低收费设置-佣金费率
      multiMiniChargeRates: [],
      // 当前触发审批按钮信息
      actionButton: {},
      // 是否展示加载loading
      loading: false,
    };
  }

  componentDidMount() {
    this.getDetailData();
  }

  // 获取地址栏参数
  @autobind
  getLocationParams() {
    const {
      location: {
        query: {
          empId, taskId, flowId, processInstanceId, flowName,
        },
      },
    } = this.props;
    return {
      empId,
      taskId,
      flowId: flowId || processInstanceId,
      processName: flowName,
    };
  }

  @autobind
  getDetailData() {
    this.showLoading();
    const { flowId } = this.getLocationParams();
    api.queryAuthCommissionDetail({
      flowId: flowId || '',
    }).then((res) => {
      if (_.isEmpty(res?.resultData)) {
        return;
      }
      const detailData = res?.resultData || {};
      this.getFlowHistory(flowId, detailData);
      this.initDetailData(detailData);
    }).catch((error) => {
      this.hiddenLoading();
      message.error(error?.message);
    });
  }

  @autobind
  getFlowHistory(flowId, detailData, submitFlag = false) {
    api.queryFlowHistory({
      flowId
    }).then((res) => {
      this.hiddenLoading();
      this.setState({ flowHistory: res?.resultData || {} });
      if (!submitFlag) {
        this.getFlowButton(detailData, res?.resultData?.currentStepName);
      }
    }).catch((error) => {
      this.hiddenLoading();
      message.error(error?.message);
    });
  }

  @autobind
  async getFlowButton(detailData, currentStepName = '') {
    const {
      taskId,
      flowId,
      empId,
      processName,
    } = this.getLocationParams();
    const orgId = _.includes(currentStepNames, currentStepName)
      ? detailData?.creatorSuperiorOrgId : detailData?.creatorOrgId;
    const res = await api.queryFlowButtons({
      bpmnName: processName,
      taskId,
      flowId,
      empId,
      id: detailData?.appId,
      orgId: orgId || '',
    });
    const buttons = !_.isEmpty(res?.resultData) ? _.reverse(res?.resultData) : [];
    this.setState({ flowButtons: buttons });
    return buttons;
  }

  @autobind
  initDetailData(detailData) {
    const multiMiniChargeRates = detailData?.multiMiniChargeSet === SELECT
      ? CUSTOM_MINIMUM_COMMISSION_RATES : MINIMUM_COMMISSION_RATES;
    this.setState({
      detailData,
      multiMiniChargeRates
    }, this.getCustInfoDetail);
  }

  @autobind
  getCustInfoDetail(params) {
    const { detailData } = this.state;
    api.queryCustInfoDetail({
      pageNum: 1,
      pageSize: 10,
      appId: detailData?.appId,
      recordId: detailData?.recordId,
      scenario: SENSORS_MODIFY,
      ...params,
    }).then((res) => {
      this.setState({ custData: res?.resultData || {} });
    });
  }

  @autobind
  getCustomerColumns() {
    const { detailData } = this.state;
    // 申请类型为【其他】-需要过滤掉渠道标识，激活时间
    let columns = [];
    if (detailData?.typeCode === OTHER_CODE) {
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['channelName', 'activeTime'], item?.key));
    } else if (detailData?.typeCode === CHANNEL_COMMISSION_CODE) {
      // 申请类型为【渠道】-需要过滤掉是否账户升佣客户
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['unifiedCommRaiseFlag'], item?.key));
    } else {
      // 申请类型为非【其他】【渠道】过滤掉渠道标识、激活时间、是否账户升佣客户
      columns = _.filter(CUSTOMER_COLUMNS, (item) => !_.includes(['unifiedCommRaiseFlag', 'channelName', 'activeTime'], item?.key));
    }
    return _.map(columns, (column) => ({
      ...column,
      render: (text) => {
        if (_.isNil(text) && _.isEmpty(text)) {
          return DEFAULT_VALUE;
        }
        return (
          <ToolTipCell
            cellText={text}
            tipContent={text}
          />
        );
      },
    }));
  }

  @autobind
  renderCommissionRate(list, twoColumns = false) {
    const { detailData } = this.state;
    return _.map(list, (item, index) => {
      const { brief, paramName } = item;
      const span = twoColumns && (_.includes(paramName, 'credit') || _.includes(paramName, 'Credit')) ? 66 : 33;
      const content = detailData?.[`${paramName}Name`]
        || COMMISSION_DEFAULT_VALUE;
      return (
        <InfoCell
          span={span}
          label={brief}
          content={content}
          key={index}
          required={_.includes(REQUIRED_PARAMS_CODES, paramName)}
        />
      );
    });
  }

  @autobind
  getButtonType(btn) {
    const { actionName } = btn;
    return actionName !== '驳回' ? 'primary' : 'default';
  }

  @autobind
  handleButtonClick(button) {
    logCommon({
      type: 'click',
      payload: {
        name: `佣金授权申请审批详情-${button?.actionName}`,
        value: button,
      },
    });
    const { detailData } = this.state;
    const { actionName, currentStepName } = button;
    const isChannel = detailData?.typeCode === CHANNEL_COMMISSION_CODE;
    if (actionName === '驳回') {
      this.setState({
        rejectModalVisible: true,
        actionButton: button,
      });
      return;
    }

    if (!isChannel && _.includes(UN_CHANNEL_APPROVAL_NODES, currentStepName)) {
      this.setState({
        countersignModalVisible: true,
        actionButton: button,
      });
      return;
    }

    if (actionName === '同意' && _.isEmpty(button?.flowAuditors)) {
      confirm({
        title: '下一步审核人员为空，请联系马珂 （001423）申请权限',
        okText: '确定',
        cancelVisible: false,
      });
      return;
    }

    this.handleDoApprove(button);
  }

  @autobind
  handleCustPageChange(pageNum) {
    logCommon({
      type: 'click',
      payload: {
        name: '佣金授权申请审批详情-客户列表-切换分页',
        value: pageNum,
      },
    });
    this.getCustInfoDetail({
      pageNum
    });
  }

  @autobind
  handleRejectModalCancel() {
    this.setState({
      rejectModalVisible: false
    });
  }

  @autobind
  showLoading() {
    this.setState({
      loading: true,
    });
  }

  @autobind
  hiddenLoading() {
    this.setState({
      loading: false,
    });
  }

  @autobind
  handleDoApprove(button, params) {
    this.showLoading();
    const {
      flowId,
      taskId,
      processName,
      empId
    } = this.getLocationParams();
    const { detailData } = this.state;
    const assignees = _.map(button?.flowAuditors, (item) => item?.empId);
    api.doApprove({
      actionName: button?.actionName,
      currentStepName: button?.currentStepName,
      flowId,
      taskId,
      bpmnName: processName,
      submitter: empId,
      assigneeList: assignees,
      businessId: detailData?.appId,
      ...params,
    }).then((res) => {
      this.hiddenLoading();
      if (button?.actionName === '办结' && !_.isEmpty(res?.resultData?.validateMsg)) {
        confirm({
          title: '提示！',
          content: res?.resultData?.validateMsg,
          okText: '确定',
          cancelVisible: false,
        });
        return;
      }
      if (res?.resultData) {
        message.success('流程发送成功');
        this.setState({
          rejectModalVisible: false,
          countersignModalVisible: false,
          flowButtons: [],
        }, () => {
          this.getFlowHistory(flowId, detailData, true);
        });
      } else {
        message.error('流程发送失败');
      }
    })
      .catch((error) => {
        this.hiddenLoading();
        const errorMessage = error?.msg || error?.message || '';
        if (error?.code === 'AC-41001') {
          confirm({
            title: '提示！',
            content: errorMessage,
            okText: '确定',
            cancelVisible: false,
          });
          return;
        }
        message.error(errorMessage);
      });
  }

  @autobind
  handleRejectModalPrimary(params) {
    const { actionButton } = this.state;
    this.handleDoApprove(actionButton, params);
  }

  @autobind
  handleCountersignModalCancel() {
    this.setState({
      countersignModalVisible: false
    });
  }

  @autobind
  handleCountersignModalPrimary(params) {
    const { actionButton, detailData, flowHistory } = this.state;
    const { flowId } = this.getLocationParams();
    this.showLoading();
    api.saveCountersignFlag({
      id: detailData?.appId,
      flowId,
      approvalType: params?.approvalType
    }).then(async (res) => {
      if (res) {
        if (params?.approvalType === UN_COUNTERSING_CODE) {
          const flowButtons = await this.getFlowButton(detailData, flowHistory?.currentStepName);
          const flowAuditors = _.find(flowButtons,
            (button) => button?.actionName === actionButton?.actionName)?.flowAuditors || [];
          const assigneeList = _.map(flowAuditors, (item) => item?.empId) || [];
          this.handleDoApprove(actionButton, { ...params, assigneeList });
          return;
        }
        this.handleDoApprove(actionButton, params);
      }
    }).catch(() => {
      this.hiddenLoading();
    });
  }

  @autobind
  handleViewFile(file, label) {
    logCommon({
      type: 'Click',
      payload: {
        name: `佣金授权申请审批详情-附件-预览${label}`,
        value: JSON.stringify(file)
      },
    });
    return viewFilePdf(file);
  }

  @autobind
  handleDownLoad(label) {
    logCommon({
      type: 'Click',
      payload: {
        name: `佣金授权申请审批详情-附件-下载${label}`,
      },
    });
  }

  render() {
    const { flowId } = this.getLocationParams();
    const {
      flowButtons,
      rejectModalVisible,
      countersignModalVisible,
      detailData,
      custData,
      flowHistory,
      multiMiniChargeRates,
      actionButton,
      loading
    } = this.state;

    const {
      contentAttachment = [],
      otherAttachment = [],
      typeName,
      adjustCommissionWayName,
      custTypeName,
      projectName,
      description,
      multiMiniChargeSet = '',
    } = detailData || {};

    // 是否展示审批按钮
    const ifShowFlowButton = flowHistory?.currentStepName !== '待客户确认' && !_.isEmpty(flowButtons);

    const contentClass = classnames({
      [styles.approvalContent]: true,
      [styles.approvalHasButton]: ifShowFlowButton,
    });

    // 客户列表分页
    const paginationOption = {
      current: custData?.curPageNum || 1,
      total: custData?.totalCount || 0,
      pageSize: custData?.pageSize || 10,
      onChange: this.handleCustPageChange,
    };

    // 是否是渠道调佣审批
    const isChannel = detailData?.typeCode === CHANNEL_COMMISSION_CODE;

    return (
      <div className={styles.approvalWrap}>
        <div className={styles.breadcrum}>佣金授权申请审批</div>
        <Loading loading={loading} />
        <div className={contentClass}>
          <DetailBlock title="基本信息">
            <InfoGroup labelWidth="190px">
              <InfoCell span={33} label="申请类型" content={typeName || DEFAULT_VALUE} />
              <InfoCell span={33} label="调佣方式" content={adjustCommissionWayName || DEFAULT_VALUE} />
              <InfoCell span={33} label="客户性质" content={custTypeName || DEFAULT_VALUE} />
              <InfoCell span={100} label="项目名称" content={projectName || DEFAULT_VALUE} />
              <InfoCell span={100} label="项目说明" content={<div className={styles.description}>{description || DEFAULT_VALUE}</div>} />
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="佣金费率设置">
            <div className={styles.commissionType}>普通账户</div>
            <InfoGroup labelWidth="190px">
              {this.renderCommissionRate(NORMAL_COMMISSION_RATES)}
            </InfoGroup>
            <div className={styles.commissionType}>信用账户</div>
            <InfoGroup labelWidth="190px">
              {this.renderCommissionRate(CREDIT_COMMISSION_RATES)}
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="账户最低收费设置">
            <InfoGroup labelWidth="190px">
              <InfoCell
                span={100}
                label="是否定制化设置最低收费"
                content={(
                  <RadioGroup
                    value={multiMiniChargeSet}
                    size="normal"
                    disabled
                  >
                    <Radio key={SELECT} value={SELECT}>是</Radio>
                    <Radio key={NO_SELECT} value={NO_SELECT}>否</Radio>
                  </RadioGroup>
                )}
              />
              {this.renderCommissionRate(multiMiniChargeRates, true)}
            </InfoGroup>
          </DetailBlock>
          <DetailBlock title="客户列表">
            <div className={styles.custListWrap}>
              <Table
                className={styles.tableWrap}
                dataSource={custData?.custInfos || []}
                columns={this.getCustomerColumns()}
                rowKey="custId"
                pagination={false}
                useNewUI
              />
              <Pagination {...paginationOption} />
            </div>
          </DetailBlock>
          <DetailBlock title="附件">
            <div className={styles.fileWrap}>
              <InfoCell
                span={100}
                label="正文附件"
                content={(
                  <Uploader
                    key="contentAttachmentUploader"
                    disabled
                    removeable={false}
                    defaultFileList={contentAttachment}
                    previewable
                    onViewFile={(file) => this.handleViewFile(file, '正文附件')}
                    onDownload={() => this.handleDownLoad('正文附件')}
                  />
                )}
              />
              <InfoCell
                span={100}
                label="其他附件"
                content={(
                  <Uploader
                    key="contentAttachmentUploader"
                    disabled
                    removeable={false}
                    defaultFileList={otherAttachment}
                    previewable
                    onViewFile={(file) => this.handleViewFile(file, '其他附件')}
                    onDownload={() => this.handleDownLoad('其他附件')}
                  />
                )}
              />
            </div>
          </DetailBlock>
          <DetailBlock title="审批记录">
            <ApprovalHistory
              data={flowHistory}
            />
          </DetailBlock>
        </div>
        <IFWrap when={ifShowFlowButton}>
          <div className={styles.buttonWrap}>
            <FlowButtons
              flowButtons={flowButtons}
              getButtonType={this.getButtonType}
              onClick={this.handleButtonClick}
            />
          </div>
        </IFWrap>
        <IFWrap when={rejectModalVisible}>
          <RejectModal
            isChannel={isChannel}
            currentStepName={actionButton?.currentStepName}
            onCancel={this.handleRejectModalCancel}
            onPrimary={this.handleRejectModalPrimary}
          />
        </IFWrap>
        <IFWrap when={countersignModalVisible}>
          <CountersignModal
            flowId={flowId || ''}
            onCancel={this.handleCountersignModalCancel}
            onPrimary={this.handleCountersignModalPrimary}
            flowAuditors={actionButton?.flowAuditors || []}
          />
        </IFWrap>
      </div>
    );
  }
}
