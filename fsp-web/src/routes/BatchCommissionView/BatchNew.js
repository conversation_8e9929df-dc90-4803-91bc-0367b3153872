/*
 * @Author: sunweibin
 * @Date: 2021-06-28 11:19:24
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-10-27 09:28:05
 * @description  批量佣金调整新建页面
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { autobind } from 'core-decorators';
import {
  Input,
  message,
  Select,
  // Alert,
} from 'antd';
import { connect } from 'dva';
import {
  NextApproverModal,
} from '@crm/biz-ui';

import Button from '@/newUI/button';
import Uploader from '@/newUI/upload';
import confirm from '@/components/common/newUI/confirm';
import IFWrap from '@/components/common/IFWrap';
import SelectComon from '@/components/common/Select';
import InfoTitle from '@/components/common/InfoTitle';
import CommissionBreadcrum from '@/components/CommissionNew/ComissionBreadcrum';
import CommissionLine from '@/components/CommissionNew/CommissionLine';
import BatchCommissionSelectGroup from '@/components/CommissionNew/BatchCommissionSelectGroup';
import BatchAddCustomer from '@/components/CommissionNew/BatchAddCustomer';
import ApproverTrigger from '@/components/CommissionNew/ApproverTrigger';
import withRouter from '@/decorators/withRouter';
import logable, { logCommon } from '@/decorators/logable';
import InfoForm from '@/components/common/infoForm';
import Icon from '@/components/common/Icon';
import { OCOMMISSION_PARAM_CODE } from '@/components/CommissionNew/BatchCommissionSelectGroup/config';
import {
  emp,
  dva,
} from '@/helper';
import attachSrc from './images/attach.svg';
import {
  ExeStyle,
  allCommissionParamName,
  canSmartCallCommissionParams,
  BATCH,
  COMMISSION_WAY_OPTIONS,
  ONLINE,
  CUSTOMER_SCOPE_OPTIONS,
  STOCK,
  // BG_KEY,
  // COMMISSION_CHANGE_TIP,
} from './config';

import styles from './index.less';

const NO_HREF = 'javascript:void(0);'; // eslint-disable-line

const TextArea = Input.TextArea;

const mapStateToProps = () => ({
});

const mapDispatchToProps = {
  deleteAttachment: dva.generateEffect('app/deleteAttachment'),
  queryApprovalList: dva.generateEffect('batchCommission/queryBatchCommissionApprovalList'),
  submitBatchCommission: dva.generateEffect('batchCommission/submitBatchCommission'),
  queryOtherCommissionOptions: dva.generateEffect('batchCommission/queryOtherCommissionOptions'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class BatchNew extends PureComponent {
  static propTypes = {
    // 附件删除
    deleteAttachment: PropTypes.func.isRequired,
    // 获取审批人列表
    queryApprovalList: PropTypes.func.isRequired,
    // 提交批量佣金申请单
    submitBatchCommission: PropTypes.func.isRequired,
    // 获取其他佣金费率选项列表
    queryOtherCommissionOptions: PropTypes.func.isRequired,
  }

  static contextTypes = {
    dict: PropTypes.object,
    goBack: PropTypes.func.isRequired,
    push: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);

    this.state = {
      // 备注
      remark: '',
      // 执行方式, 默认为【否】
      executionStyle: ExeStyle.NO,
      // 目标股基佣金率
      newCommission: '',
      // 附件信息
      attachment: '',
      // 重置其他佣金费率选项
      otherComReset: new Date().getTime(),
      // 上传附件是否禁用
      uploaderDisabled: false,
      // 删除附件是否禁用删除
      uploaderRemove: true,
      // 下一步审批人列表
      approverList: [],
      // 下一步审批人弹框
      nextApproverVisible: false,
      // 下一步审批人
      approverName: '',
      approverId: '',
      // 提交后，提交按钮影藏
      submitBtnHide: false,
      // 其他佣金费率值
      otherCommissions: {},
      // 其他佣金费率选项
      otherCommissionOptions: [],
      // 目标股基佣金率配置数据
      targetGJlist: [],
      // 调佣方式-默认线下
      adjustCommissionWayCode: ONLINE,
      // 客户范围-默认存量客户
      customerScopeCode: STOCK,
    };

    this.clearDefaultValue = {
      // 备注
      remark: '',
      // 执行方式, 默认为【否】
      executionStyle: ExeStyle.NO,
      // 目标股基佣金率
      newCommission: '',
      // 附件信息
      attachment: '',
      // 客户
      custIds: [],
      // 其他佣金费率
      otherCommissions: {},
      // 审批人
      approverId: '',
    };
  }

  componentDidMount() {
    this.getComissionOptions();
    // 进入页面查一把子类型的审批人列表
    this.props.queryApprovalList({
      bpmnName: '批量佣金调整审批流程',
      actionName: '提交',
    }).then((approverList) => this.setState({ approverList }));
  }

  @autobind
  getComissionOptions(params = {}) {
    // 根据“佣金管理配置”，查询新的其他佣金费率选项列表接口
    this.props.queryOtherCommissionOptions({
      // 因为使用公用的查其他佣金字典接口，需要传佣金调整子类型key值给后端
      scene: BATCH,
      subType: STOCK,
      ...params,
    }).then((res) => {
      const targetGJlist = this.getGjSelectOptions(res?.targetGJComRatio);
      this.setState({
        // 其他佣金费率
        otherCommissionOptions: res?.otherRatio || [],
        // 目标股基佣金率
        targetGJlist,
      });
    });
  }

  // 当客户范围变动，可选的佣金率下拉项需重置
  resetComissionOptions(value) {
    this.setState({
      // 其他佣金费率
      otherCommissionOptions: [],
      // 目标股基佣金率
      targetGJlist: [],
    }, () => this.getComissionOptions({ subType: value }));
  }

  @autobind
  getGjSelectOptions(data) {
    if (_.isEmpty(data)) {
      return [];
    }
    const newOptions = data.map((item) => ({
      label: item.codeDesc,
      value: item.codeValue,
      ...item,
    }));
    return _.concat([{ label: '请选择', value: '' }], newOptions);
  }

  @autobind
  handleChangeRemark(e) {
    this.setState({
      remark: e.target.value,
    });
  }

  @autobind
  clearCustData() {
    this.batchAddCustomer.clearCustList();
  }

  // 客户内容组件
  // 由于组件被connect包装过所以需要使用getWrappedInstance获取真实的组件
  @autobind
  batchAddCustomerRef(warp) {
    if (!warp) return;
    this.batchAddCustomer = warp.getWrappedInstance();
  }

  // 2023-09-25 经手费更新需求-目标股基佣金费率交互由手动输入改为下拉选择
  @autobind
  handleChangeGJCommission(name, value) {
    // if (!_.isEmpty(this.state?.custIds)) {
    //   confirm({
    //     title: '提示',
    //     content: '修改后将清空客户列表已有数据，请确认！',
    //     okText: '确定',
    //     onOk: () => {
    //       this.setState({ newCommission: value, custIds: [] });
    //       this.clearCustData();
    //     },
    //   });
    //   return;
    // }
    this.setState({ newCommission: value });
  }

  // 选择其他佣金比率
  @autobind
  changeOtherCommission(name, value, { label = '' }) {
    this.setState((pre) => ({
      otherCommissions: {
        ...pre.otherCommissions,
        [name]: value,
        [`${name}Name`]: label,
      }
    }));
  }

  @autobind
  handleChangeChangeOtherCommission(name, value, option) {
    // if (!_.isEmpty(this.state?.custIds)) {
    //   confirm({
    //     title: '提示',
    //     content: '修改后将清空客户列表已有数据，请确认！',
    //     okText: '确定',
    //     onOk: () => {
    //       this.changeOtherCommission(name, value, option, true);
    //       this.clearCustData();
    //       this.setState({
    //         custIds: [],
    //       });
    //     },
    //   });
    //   return;
    // }
    this.changeOtherCommission(name, value, option);
  }

  // 上传成功后的数据
  @autobind
  handleUploadSuccess(apiResult) {
    const { attachment, attaches } = apiResult.resultData;
    this.setState({ attachment });
    return Promise.resolve(_.last(attaches));
  }

  // 删除已经上传成功的附件
  @autobind
  handleFileRemove({ attachId, status }) {
    // 如果要删除的文件没有attachId，或者status的状态为error，则表示该附件没有上传到服务器中
    const { attachment } = this.state;
    if (attachment && !_.isEmpty(attachId) && status !== 'error') {
      return this.props.deleteAttachment({
        empId: emp.getId(),
        attachId,
        attachment,
      });
    }
    // 默认表示删除成功
    return Promise.resolve(true);
  }

  @autobind
  @logable({
    type: 'CLick',
    payload: {
      name: '新建批量佣金调整-打开下一步审批人弹框',
    },
  })
  handleNextApproverTrigger() {
    this.setState({ nextApproverVisible: true });
  }

  @autobind
  @logable({
    type: 'CLick',
    payload: {
      name: '新建批量佣金调整-下一步审批人弹框-关闭',
    },
  })
  handleNextApproverModalClose() {
    this.setState({ nextApproverVisible: false });
  }

  @autobind
  @logable({
    type: 'CLick',
    payload: {
      name: '新建批量佣金调整-下一步审批人弹框-取消',
    },
  })
  handleNextApproverModalCancel() {
    this.setState({ nextApproverVisible: false });
  }

  @autobind
  @logable({
    type: 'CLick',
    payload: {
      name: '新建批量佣金调整-下一步审批人弹框-确定',
    },
  })
  handleNextApproverModalConfirm(approvers) {
    // 因为下一步审批人弹框，确认返回的是数组，所以需要去第一个
    const approver = _.head(approvers);
    this.setState({
      nextApproverVisible: false,
      approverName: approver?.empName || '',
      approverId: approver?.empId || '',
    });
  }

  // 将用户选择添加的客户列表返回到弹出层，以便提交试用（批量佣金）
  @autobind
  handlePassData(list) {
    this.setState({
      custIds: list,
    });
  }

  @autobind
  handleGoBack() {
    this.context.push({
      pathname: '/businessApplyment/commission',
    });
  }

  // 批量调佣-智能外呼执行方式-用户是否选择了不能外呼的其他佣金费率
  @autobind
  checkHasCannotSmartCallCommissions() {
    const cannotSmartCallCommissionsParams = _.filter(allCommissionParamName, (param) => (
      !_.includes(canSmartCallCommissionParams, param)
    ));
    const cannotSmartCallCommissions = _.pick(
      this.state.otherCommissions, cannotSmartCallCommissionsParams
    );
    const noCannotSmartCallCommissions = _.every(cannotSmartCallCommissions, _.isEmpty);
    return !noCannotSmartCallCommissions;
  }

  // 智能外呼下，判断A股佣金（目标股基佣金率）、场内基金、债券和回购费率，是否有值
  @autobind
  isNotSetCommissionInSmartCall() {
    const { otherCommissions, newCommission } = this.state;
    const smartCallNeedCommissions = _.pick(otherCommissions, canSmartCallCommissionParams);
    // 判断【智能外呼】下场内基金、债券和回购费率，是否均为空
    const isEveryEmpty = _.every(smartCallNeedCommissions, _.isEmpty);

    return isEveryEmpty && _.isEmpty(newCommission);
  }

  @autobind
  checkOtherComsIsNull() {
    // 判断【目标股基佣金率】与【其他佣金费率】是否选择了，如果一个都没选择过，则提交时报错
    const { newCommission, otherCommissions } = this.state;
    const otherIsNull = _.isEmpty(otherCommissions) || _.every(otherCommissions, _.isEmpty);
    return otherIsNull && _.isEmpty(newCommission);
  }

  @autobind
  submitCheck() {
    const {
      approverId,
      custIds,
      executionStyle,
      otherCommissions,
    } = this.state;
    const isSmartCall = executionStyle === ExeStyle.YES;
    // 用户是否选择了(场内基金、债券、回购)之外的其他佣金费率
    const hasCannotSmartCallCommissions = this.checkHasCannotSmartCallCommissions();
    // 判断什么时候能够提交
    // 新增 用户选了(场内基金、债券、回购)之外的其他佣金费率时不能选择智能外呼执行方式，优先校验
    if (isSmartCall && hasCannotSmartCallCommissions) {
      message.error('智能外呼调佣只支持调整A股佣金、场内基金、债券和回购费率，不适用于调整其他佣金费率');
      return false;
    }
    // 如果是智能外呼，则【A股佣金】、【场内基金】、【债券】、【回购费率】和目标股基佣金率，必须要有一个有值
    if (isSmartCall && this.isNotSetCommissionInSmartCall()) {
      message.error('智能外呼调佣下，【A股佣金】、【场内基金】、【债券】、【回购费率】至少要设置一个');
      return false;
    }
    // 如果选择的是【非智能外呼】，则需要判断【目标股基佣金率】或者【其他佣金率】有选项
    if (!isSmartCall && this.checkOtherComsIsNull()) {
      message.error('非智能外呼下，目标股基佣金率和其他佣金费率中至少设置一个！');
      return false;
    }
    if (_.isEmpty(custIds)) {
      message.error('请添加客户');
      return false;
    }
    if (_.isEmpty(approverId)) {
      message.error('审批人员不能为空');
      return false;
    }
    if (_.isEmpty(otherCommissions?.[OCOMMISSION_PARAM_CODE])) {
      message.error('场内基金不能为空');
      return false;
    }

    return true;
  }

  @autobind
  handleSubmit() {
    if (!this.submitCheck()) return;

    const {
      remark,
      newCommission,
      approverId,
      custIds,
      attachment,
      executionStyle,
      adjustCommissionWayCode,
      customerScopeCode,
      otherCommissions,
    } = this.state;
    // 执行方式是否是智能外呼
    const isSmartCall = executionStyle === ExeStyle.YES;
    // 批次id
    const { batchId } = this.batchAddCustomer.state;
    // 配额数
    const quotaNumber = _.size(custIds);
    // 挑选出用户选择的其他佣金率
    const submitParams = {
      // TODO: 此参数是否还需要？
      custLists: {},
      batchId,
      newCommsion: newCommission,
      aprovaluser: approverId,
      remark,
      loginUser: emp.getId(),
      orgId: emp.getOrgId(),
      ...otherCommissions,
      custIds,
      attachmentNum: attachment,
      executionStyleCode: executionStyle,
      isSmartCall,
      quotaNumber,
      // TODO: 目前因为本页面只会在有【新功能体验岗】权限下使用，因此此处直接给 true，
      // TODO: 后期放开灰度，需要研究是否前后端需要统一修改
      hasNewFlow: true,
      // 调佣方式
      adjustCommissionWayCode,
      adjustCommissionWayName: this.getOptionName(COMMISSION_WAY_OPTIONS, adjustCommissionWayCode),
      customerScopeCode,
    };
    if (isSmartCall) {
      confirm({
        title: '提示',
        content: '智能外呼申请流程提交后无法终止撤回，请谨慎确认客户信息并明确相关风险',
        cancelText: '返回',
        onOk: () => this.finalBatchSubmit(submitParams),
      });
      return;
    }
    this.finalBatchSubmit(submitParams);
  }

  @autobind
  @logable({
    type: 'CLick',
    payload: {
      name: '新建批量佣金调整-下载提交失败附件',
    },
  })
  handleDownloadClick() {}

  @autobind
  getSubmitResContent(resolveData) {
    const {
      errorMsg = '',
      fileId,
      fileName,
    } = resolveData;
    if (!_.isEmpty(fileId)) {
      return (
        <div>
          <div>{errorMsg}</div>
          <div className={styles.failAttachment}>
            <img src={attachSrc} alt="icon" />
            <a
              onClick={this.handleDownloadClick}
              href={
                _.isEmpty(fileId)
                  ? NO_HREF
                  : `/fspa/mcrm/api/storage/download?fileId=${fileId}`
              }
            >
              {fileName}
            </a>
          </div>
        </div>
      );
    }
    return errorMsg;
  }

  @autobind
  finalBatchSubmit(submitParams) {
    this.props.submitBatchCommission(submitParams).then(
      (resolveData) => {
        const {
          hasError = false,
          hasInvalidAccount = false, // 是否有校验不合格的账户
        } = resolveData;
        // 2025-08-29新增全渠道升佣校验，校验不通过弹框展示后端返回提示语
        // if (!resolveData?.pass) {
        //   confirm({
        //     title: '提示',
        //     content: resolveData?.errorMsg,
        //     cancelVisible: false,
        //   });
        //   return;
        // }
        if (hasError || hasInvalidAccount) {
          confirm({
            title: '提示',
            content: this.getSubmitResContent(resolveData),
            cancelVisible: false,
          });
          return;
        }
        message.success('批量佣金调整提交成功');
        this.setState({
          submitBtnHide: true,
          uploaderDisabled: true,
          uploaderRemove: false,
        }, this.handleGoBack);
      },
      () => {
        message.error('批量佣金调整提交失败');
      },
    );
    // log日志 --- 批量佣金调整提交
    logCommon({
      type: 'Submit',
      payload: {
        name: '新建批量佣金调整-提交',
        value: JSON.stringify(submitParams),
      },
    });
  }

  @autobind
  renderOption(options) {
    return _.map(options, ({ label, value }) => (
      <Select.Option key={value} value={value}>{label}</Select.Option>
    ));
  }

  // 是否是线上调佣
  @autobind
  judgeIsOnline() {
    const { adjustCommissionWayCode } = this.state;
    return adjustCommissionWayCode === ONLINE;
  }

  @autobind
  handleCommissionWayChange(value) {
    confirm({
      title: '提示',
      content: '修改调佣方式，将会清空其余数据，确认修改吗？',
      onOk: () => {
        this.setState({
          adjustCommissionWayCode: value,
        });
        // 清空其他数据的值
        this.clearAllData();
        // 清空客户数据
        this.clearCustData();
      },
    });
    logCommon({
      type: 'Select',
      payload: {
        name: '批量佣金调整-新建-选择调佣方式',
        value,
      }
    });
  }

  @autobind
  handleCustomerScopeChange(value) {
    // 由存量改为活动期间客户时清空选项数据
    confirm({
      title: '提示',
      content: '修改客户范围，将会清空其余数据，确认修改吗？',
      onOk: () => {
        this.setState({
          customerScopeCode: value,
        }, () => this.resetComissionOptions(value));
        // 清空其他数据的值
        this.clearAllData();
        // 清空客户数据
        this.clearCustData();
      },
    });

    logCommon({
      type: 'Select',
      payload: {
        name: '批量佣金调整-新建-选择客户范围',
        value,
      }
    });
  }

  // 清空除调佣方式&&客户范围以外的其他值
  @autobind
  clearAllData() {
    this.setState({
      ...this.clearDefaultValue,
    });
    // 清空客户输入框的内容
    if (this.batchAddCustomer) {
      this.batchAddCustomer.clearInputValue();
    }
  }

  @autobind
  getOptionName(list, value) {
    return _.find(list, (item) => item.value === value)?.label || '';
  }

  render() {
    const {
      remark,
      newCommission,
      attachment,
      otherComReset,
      nextApproverVisible,
      approverList,
      approverId,
      approverName,
      submitBtnHide,
      uploaderDisabled,
      uploaderRemove,
      otherCommissionOptions,
      adjustCommissionWayCode,
      customerScopeCode,
      executionStyle,
      otherCommissions,
      targetGJlist,
    } = this.state;

    // 发送客户校验请求时的其他参数
    const otherPayload = {
      businessType: 'BatchProcess',
      newCommission,
      prodCode: null, // 由于股基佣金和资讯产品订阅解绑，批量佣金校验接口该字段传null，避免有未办结的流程导致报错
      ignoreCatch: true,
      executionStyleCode: executionStyle,
      // 客户范围
      subBusinessType: customerScopeCode,
    };

    // 调佣方式是否是线上
    const isOnline = this.judgeIsOnline();

    return (
      <div className={styles.pageContainer}>
        <CommissionBreadcrum name="新建批量佣金调整" />
        <div className={styles.content}>
          <div className={styles.approvalBlock}>
            <InfoTitle head="基本信息" />
            <CommissionLine label="子类型" labelWidth="135px" required>
              <div className={styles.textItem}>批量佣金调整</div>
            </CommissionLine>
            <InfoForm
              label="调佣方式"
              style={{ width: '135px', verticalAlign: 'top', marginRight: '0' }}
              required
            >
              <Select
                style={{ width: '200px' }}
                value={adjustCommissionWayCode}
                onChange={this.handleCommissionWayChange}
                suffixIcon={<Icon type="xiangxia" className={styles.xiangxiaIcon} />}
              >
                {this.renderOption(COMMISSION_WAY_OPTIONS)}
              </Select>
            </InfoForm>
            <InfoForm
              label="客户范围"
              style={{ width: '135px', verticalAlign: 'top', marginRight: '0' }}
              required
            >
              <Select
                style={{ width: '200px' }}
                value={customerScopeCode}
                onChange={this.handleCustomerScopeChange}
                suffixIcon={<Icon type="xiangxia" className={styles.xiangxiaIcon} />}
              >
                {this.renderOption(CUSTOMER_SCOPE_OPTIONS)}
              </Select>
            </InfoForm>
            <CommissionLine label="备注" labelWidth="135px">
              <TextArea
                placeholder="备注内容"
                value={remark}
                onChange={this.handleChangeRemark}
              />
            </CommissionLine>
          </div>
          <div className={styles.approvalBlock}>
            <InfoTitle head="佣金产品选择" />
            {/* <Alert message={COMMISSION_CHANGE_TIP} type="warning" showIcon /> */}
            <CommissionLine
              label="目标股基佣金率"
              labelWidth="135px"
              needInputBox={false}
            >
              <SelectComon
                name="newCommission"
                data={targetGJlist}
                value={newCommission}
                onChange={this.handleChangeGJCommission}
                className={styles.selectWarp}
                needShowKey={false}
              />
            </CommissionLine>
          </div>
          <div className={styles.approvalBlock}>
            <InfoTitle head="其他佣金费率" />
            <BatchCommissionSelectGroup
              reset={otherComReset}
              onChange={this.handleChangeChangeOtherCommission}
              otherRatio={otherCommissionOptions}
              isOnline={isOnline}
              baseCommission={otherCommissions}
            />
          </div>
          <div className={styles.approvalBlock}>
            <InfoTitle head="客户" />
            <BatchAddCustomer
              disabled={submitBtnHide}
              ref={this.batchAddCustomerRef}
              onPassData={this.handlePassData}
              otherPayload={otherPayload}
              executionStyle={executionStyle}
              adjustCommissionWayCode={adjustCommissionWayCode}
              customerScopeCode={customerScopeCode}
              otherCommissions={{ ...otherCommissions, newCommission }}
            />
          </div>
          <IFWrap when={!isOnline}>
            <div className={styles.approvalBlock}>
              <InfoTitle head="附件信息" />
              <Uploader
                key="batchCreatBoardUploader"
                disabled={uploaderDisabled}
                removeable={uploaderRemove}
                attachment={attachment}
                onSuccess={this.handleUploadSuccess}
                onRemove={this.handleFileRemove}
              />
            </div>
          </IFWrap>
          <div className={styles.approvalBlock}>
            <InfoTitle head="审批人" />
            <CommissionLine label="选择审批人" labelWidth="110px">
              <ApproverTrigger
                onClick={this.handleNextApproverTrigger}
                name={approverName}
                id={approverId}
              />
            </CommissionLine>
          </div>
          <div className={styles.approvalBlock}>
            <div className={styles.flowButtons}>
              <IFWrap when={!submitBtnHide}>
                <Button type="primary" onClick={this.handleSubmit}>提交</Button>
              </IFWrap>
            </div>
          </div>
          <NextApproverModal
            visible={nextApproverVisible}
            onClose={this.handleNextApproverModalClose}
            onCancel={this.handleNextApproverModalCancel}
            onConfirm={this.handleNextApproverModalConfirm}
            dataSource={approverList}
          />
        </div>
      </div>
    );
  }
}
