/*
 * @Description: 自定义海报配置
 * @Author: lijingjing
 * @Date: 2022-04-25 17:11:09
 * @Last Modified by: weiting
 * @Last Modified time: 2022-05-06 10:39:58
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'dva';
import {
  dva,
} from '@/helper';
import { Tabs } from 'antd';
import { map, filter } from 'lodash';
import withRouter from '@/decorators/withRouter';
import { autobind } from 'core-decorators';
import EditPosterList from '@/components/materialLibrary/EditPosterList';
import {
  TAB_CONFIG,
} from './config';
import styles from './home.less';
import { logCommon } from '../../decorators/logable';

const dispatch = dva.generateEffect;

const TabPane = Tabs.TabPane;

const mapStateToProps = (state) => ({
  // 海报列表
  posterList: state.materialLibrary.posterList,
});

const mapDispatchToProps = {
  // 获取海报列表
  queryPosterList: dispatch('materialLibrary/queryPosterList'),
  // 保存海报列表
  savePosterList: dispatch('materialLibrary/savePosterList'),
};

@connect(mapStateToProps, mapDispatchToProps)
@withRouter
export default class Home extends PureComponent {
  static propTypes = {
    // 获取海报列表
    queryPosterList: PropTypes.func.isRequired,
    // 保存海报列表
    savePosterList: PropTypes.func.isRequired,
    // 海报列表
    posterList: PropTypes.array.isRequired,
  }

  static contextTypes = {
    replace: PropTypes.func.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  componentDidMount() {
    this.getPosterList();
  }

  @autobind
  getPosterList(type) {
    this.props.queryPosterList();
  }

  @autobind
  renderBirthdayTab() {
    const { posterList, savePosterList } = this.props;

    return (
      <div className={styles.birthdayWrap}>
        <EditPosterList
          posterList={posterList}
          savePosterList={savePosterList}
          queryPosterList={() => this.getPosterList()}
        />
      </div>
    );
  }

  render() {
    return (
      <div className={styles.home}>
        <Tabs>
          {
            map(TAB_CONFIG, (item) => (
              <TabPane tab={item.name} key={item.key}>
                <div className={styles.tabPaneWrap}>
                  {
                    this.renderBirthdayTab()
                  }
                </div>
              </TabPane>
            ))
          }
        </Tabs>
      </div>
    );
  }
}
