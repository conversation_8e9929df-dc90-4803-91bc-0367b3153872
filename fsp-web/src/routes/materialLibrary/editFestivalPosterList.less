.editFestivalPosterListWrap {
  padding: 20px 30px;
  background: #fff;
  .header {
    height: 48px;
    display: flex;
    align-items: center;
    padding-left: 8px;
    position: relative;
    border-bottom: 1px solid #e9e9e9;
    margin-bottom: 10px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      width: 3px;
      height: 15px;
      background: #108ee9;
      transform: translateY(-50%);
    }

    .info {
      display: flex;

      .title {
        color: #666;
        font-size: 14px;
        line-height: 20px;
      }
      .infoDetail {
        color: #333;
        font-size: 14px;
        line-height: 20px;
      }

      & + .info {
        margin-left: 30px;
      }
    }
  }
}
